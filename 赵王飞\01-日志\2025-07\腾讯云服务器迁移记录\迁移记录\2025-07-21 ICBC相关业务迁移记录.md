
# 淖尔e钱包
#### 雷池
https://naoer.icbc.51zsqc.com/
naoer.icbc.51zsqc.com
http://127.0.0.1:20031
#### 容器配置
##### bridge
- 名称: icbc-naoer-bridge
- 目录: /mnt/datadisk0/volumns/icbc/naoer/bridge
- 网络: 1panel-network
- 端口: 31802:22222
- 目录: 
    - /mnt/datadisk0/volumns/icbc/naoer/bridge:/workdir
- 命令：java -jar /workdir/JavaBridge.jar HTTP:22222

##### web
- 名称: icbc-naoer-web
- 端口：20031 : 80
- 
- 目录:
    - /docker-data/web/php.ini : /etc/php/7.2/apache2/php.ini
    - /docker-data/web/apache_default.conf : /etc/apache2/sites-enabled/default.conf
    - /mnt/datadisk0/volumns/icbc/naoer/web : /var/www
    - /etc/localtime : /etc/localtime
- 命令：apache2-foreground

```

### 原始配置
naoer.php
```php
const DB_HOST = "************";
const DB_NAME = "icbc_naoer";
const DB_USER = "icbc_naoer";
const DB_PWD = "7D51A5B1992D@h*";
const DB_PORT = "3306";
const DB_LOG_HOST = "************";
const DB_LOG_NAME = "icbc_naoer_log";
const DB_LOG_USER = "icbc_naoer";
const DB_LOG_PWD = "7D51A5B1992D@h*";
const DB_LOG_PORT = "3306";
const REDIS_HOST = "************";
const REDIS_PORT = 30079;
define("JAVA_HOSTS", "************:31802");
```

需要使用sed命令
把数据库 IP替换为mysql-prod
Redis 替换为redis-prod
Javahosts替换为icbc-naoer-bridge:22222

```bash
cd /mnt/datadisk0/volumns/icbc/naoer/web/env
cp -r naoer.php naoer.php.bak
sed -i 's/************/mysql-prod/g' naoer.php
sed -i 's/************:31802/icbc-naoer-bridge:22222/g' naoer.php
sed -i 's/************/redis-prod/g' naoer.php
sed -i 's/30079/6379/g' naoer.php
cat naoer.php
```

```sql
CREATE DATABASE `icbc_naoer` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */;
CREATE DATABASE `icbc_naoer_log` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */;

CREATE USER 'icbc_naoer' @'%' IDENTIFIED BY '7D51A5B1992D@h*';
GRANT ALL PRIVILEGES ON `icbc_naoer`.* TO 'icbc_naoer' @'%' IDENTIFIED BY '7D51A5B1992D@h*';
GRANT ALL PRIVILEGES ON `icbc_naoer_log`.* TO 'icbc_naoer' @'%' IDENTIFIED BY '7D51A5B1992D@h*';
FLUSH PRIVILEGES;
```

更改目录权限
```bash
chmod -R a+w /mnt/datadisk0/volumns/icbc/naoer/web/runtime
chmod -R a+w /mnt/datadisk0/volumns/icbc/naoer/web/icbc
chmod -R a+w /mnt/datadisk0/volumns/icbc/naoer/web/application/icbc/service/sftp_temp
```

### 替换jssdk文件内容
```bash
# 原来的接口已经无法使用,先备份,然后使用sed命令替换jssdk方法返回空内容
cp -r /mnt/datadisk0/volumns/icbc/naoer/web/application/icbc/home/<USER>/mnt/datadisk0/volumns/icbc/naoer/web/application/icbc/home/<USER>
sed -i 's/return json_success($sdkJson);/return json_success([]);/g' /mnt/datadisk0/volumns/icbc/naoer/web/application/icbc/home/<USER>
sed -i '/\$sdkJson=\$js->buildConfig(\$APIs, \$debug = false, \$beta = false, \$json = true);/d' /mnt/datadisk0/volumns/icbc/naoer/web/application/icbc/home/<USER>
```

查看web容器日志
```bash
docker exec -it icbc-naoer-web tail -f /var/log/apache2/access.log
docker exec -it icbc-naoer-web tail -f /var/log/apache2/error.log
```

# 赤峰那仁泰

```
https://cf.icbc.51zsqc.com/
cf.icbc.51zsqc.com
http://127.0.0.1:20032
```
测试地址
https://cf.icbc.51zsqc.com/h5/
https://cf.icbc.51zsqc.com/admin.php
#### 容器配置
##### bridge
- 名称: icbc-chifeng-bridge
- 端口: 30454:22222
- 网络: 1panel-network
- 目录:
    - /mnt/datadisk0/volumns/icbc/chifeng/bridge:/workdir
- 命令：java -jar /workdir/JavaBridge.jar HTTP:22222

##### web
- 名称: icbc-chifeng-web
- 端口：20032 : 80
- 网络: 1panel-network
- 目录:
    - /docker-data/web/php.ini : /etc/php/7.2/apache2/php.ini
    - /docker-data/web/apache_default.conf : /etc/apache2/sites-enabled/default.conf
    - /mnt/datadisk0/volumns/icbc/chifeng/web : /var/www
    - /etc/localtime : /etc/localtime
- 命令：apache2-foreground

目录地址/mnt/datadisk0/volumns/icbc/chifeng
- web
    - runtime
    - public
    ...
- bridge

## 容器目录映射


## 数据库迁移
```sql
show create database icbc_chifeng;
> 
CREATE DATABASE `icbc_chifeng` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */
CREATE DATABASE `icbc_chifeng_log` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */
```

## 配置文件调整
cf.php
```php
const DB_HOST = "************";
const DB_NAME = "icbc_chifeng";
const DB_USER = "icbc_chifeng";
const DB_PWD = "zKCbPFayLmM6nZnl";
const DB_PORT = "3306";

const DB_LOG_HOST = "************";
const DB_LOG_NAME = "icbc_chifeng_log";
const DB_LOG_USER = "icbc_chifeng";
const DB_LOG_PWD = "zKCbPFayLmM6nZnl";
const DB_LOG_PORT = "3306";
const REDIS_HOST = "************";
const REDIS_PORT = 30079;
define("JAVA_HOSTS", "************:30454");
```

```bash
cd /mnt/datadisk0/volumns/icbc/chifeng/web/env
sed -i 's/************/mysql-prod/g' cf.php
sed -i 's/************:30454/icbc-chifeng-bridge:22222/g' cf.php
sed -i 's/************/redis-prod/g' cf.php
sed -i 's/30079/6379/g' cf.php
cat cf.php
```

### 创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE `icbc_chifeng` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */;
CREATE DATABASE `icbc_chifeng_log` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */;

-- 创建用户
CREATE USER 'icbc_chifeng' @'%' IDENTIFIED BY 'zKCbPFayLmM6nZnl';
GRANT ALL PRIVILEGES ON `icbc_chifeng`.* TO 'icbc_chifeng' @'%' IDENTIFIED BY 'zKCbPFayLmM6nZnl';
GRANT ALL PRIVILEGES ON `icbc_chifeng_log`.* TO 'icbc_chifeng' @'%' IDENTIFIED BY 'zKCbPFayLmM6nZnl';
FLUSH PRIVILEGES;
```

### -f查看容器日志 /var/log/apache2/access.log、error.log
```bash
docker exec -it icbc-chifeng-web tail -f /var/log/apache2/access.log
docker exec -it icbc-chifeng-web tail -f /var/log/apache2/error.log
```

### 更改runtime目录权限,添加可写权限
```bash
chmod -R a+w /mnt/datadisk0/volumns/icbc/chifeng/web/runtime
chmod -R a+w /mnt/datadisk0/volumns/icbc/chifeng/web/icbc
chmod -R a+w /mnt/datadisk0/volumns/icbc/chifeng/web/application/icbc/service/sftp_temp
```

### 替换jssdk文件内容
application\icbc\home\Jssdk.php
```bash
# 原来的接口已经无法使用,先备份,然后使用sed命令替换jssdk方法返回空内容
cp -r /mnt/datadisk0/volumns/icbc/chifeng/web/application/icbc/home/<USER>/mnt/datadisk0/volumns/icbc/chifeng/web/application/icbc/home/<USER>
sed -i 's/return json_success($sdkJson);/return json_success([]);/g' /mnt/datadisk0/volumns/icbc/chifeng/web/application/icbc/home/<USER>
sed -i '/\$sdkJson=\$js->buildConfig(\$APIs, \$debug = false, \$beta = false, \$json = true);/d' /mnt/datadisk0/volumns/icbc/chifeng/web/application/icbc/home/<USER>
```

## 中建国信
/mnt/datadisk0/volumns/icbc/zjgx
### 容器配置
#### bridge
- 名称: icbc-zjgx-bridge
- 网络: 1panel-network
- 目录: /mnt/datadisk0/volumns/icbc/zjgx/bridge
- 命令：java -jar /workdir/JavaBridge.jar HTTP:22222

```bash


```


#### web
- 名称: icbc-zjgx-web
- 端口：20033 : 80
- 网络: 1panel-network
- 目录:
    - /docker-data/web/php.ini : /etc/php/7.2/apache2/php.ini
    - /docker-data/web/apache_default.conf : /etc/apache2/sites-enabled/default.conf
    - /mnt/datadisk0/volumns/icbc/zjgx/web : /var/www
    - /etc/localtime : /etc/localtime
- 命令：apache2-foreground


## 定时任务
crontab -l
```
sudo mkdir /crontab
cd /crontab
sudo vi /crontab/crontab
crontab /crontab/crontab
crontab -l
```


## 其他一些无用的备份

··
# 使用 zip 命令，压缩
cd /data/nfs_share/html/icbc_html
ls -l
drwxr-xr-x 21 <USER> <GROUP>     4096 Apr 29  2024 icbc_wyjd
drwxr-xr-x 21 <USER> <GROUP>     4096 May 21  2024 icbc_zjej

zip -r icbc_wyjd.zip icbc_wyjd
zip -r icbc_zjej.zip icbc_zjej

