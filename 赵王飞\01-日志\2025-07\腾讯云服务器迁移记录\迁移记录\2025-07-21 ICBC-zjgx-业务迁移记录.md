# 中建国信
https://zjgx.icbc.51zsqc.com/
zjgx.icbc.51zsqc.com
http://127.0.0.1:20033

## 容器配置
### bridge
-   **名称**: icbc-zjgx-bridge
-   **网络**: 1panel-network
-   **目录**: /mnt/datadisk0/volumns/icbc/zjgx/bridge
-   **命令**: java -jar /workdir/JavaBridge.jar HTTP:22222

### web
-   **名称**: icbc-zjgx-web
-   **端口**: 20033 : 80
-   **网络**: 1panel-network
-   **目录**:
    -   /docker-data/web/php.ini : /etc/php/7.2/apache2/php.ini
    -   /docker-data/web/apache_default.conf : /etc/apache2/sites-enabled/default.conf
    -   /mnt/datadisk0/volumns/icbc/zjgx/web : /var/www
    -   /etc/localtime : /etc/localtime
-   **命令**: apache2-foreground

## 配置文件调整
`zjgx.php` 的原始配置:
```php
const DB_HOST = "************";
const DB_NAME = "icbc_zjgx";
const DB_USER = "icbc_zjgx";
const DB_PWD = "21DCBA30DFFF959E";
const REDIS_HOST = "************";
const REDIS_PORT = 30079;
define("JAVA_HOSTS", "************:32557");
```

## 创建数据库和用户
```sql
-- 在 mysql-prod 容器内通过 root 用户执行
CREATE DATABASE IF NOT EXISTS \`icbc_zjgx\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
CREATE DATABASE IF NOT EXISTS \`icbc_zjgx_log\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin;
CREATE USER IF NOT EXISTS 'icbc_zjgx'@'%' IDENTIFIED BY '21DCBA30DFFF959E';
GRANT ALL PRIVILEGES ON \`icbc_zjgx\`.* TO 'icbc_zjgx'@'%';
GRANT ALL PRIVILEGES ON \`icbc_zjgx_log\`.* TO 'icbc_zjgx'@'%';
FLUSH PRIVILEGES;
```

## 完整执行命令记录

**1. 查找配置文件**
```bash
# 查找配置文件
ls -l /mnt/datadisk0/volumns/icbc/zjgx/web/env/

# 查看配置文件内容
cat /mnt/datadisk0/volumns/icbc/zjgx/web/env/zjgx.php
```

**2. 创建Docker容器**
```bash
# 创建 bridge 容器
docker run -d --name icbc-zjgx-bridge --network 1panel-network --restart=always -v /mnt/datadisk0/volumns/icbc/zjgx/bridge:/workdir java:8-jre java -jar /workdir/JavaBridge.jar HTTP:22222

# 创建 web 容器
docker run -d --name icbc-zjgx-web --network 1panel-network --restart=always -p 20033:80 -v /docker-data/web/php.ini:/etc/php/7.2/apache2/php.ini -v /docker-data/web/apache_default.conf:/etc/apache2/sites-enabled/default.conf -v /mnt/datadisk0/volumns/icbc/zjgx/web:/var/www -v /etc/localtime:/etc/localtime zhao0829wang/apache2-php:7.2-rc4 apache2-foreground
```

**3. 修改配置文件**
```bash
# 备份并修改配置文件
cd /mnt/datadisk0/volumns/icbc/zjgx/web/env && \
cp zjgx.php zjgx.php.bak && \
sed -i 's/************/mysql-prod/g' zjgx.php && \
sed -i 's/************/redis-prod/g' zjgx.php && \
sed -i 's/30079/6379/g' zjgx.php && \
sed -i 's/************:32557/icbc-zjgx-bridge:22222/g' zjgx.php

# 修正上一条命令中因执行顺序导致的错误
sed -i 's/redis-prod:32557/icbc-zjgx-bridge:22222/g' /mnt/datadisk0/volumns/icbc/zjgx/web/env/zjgx.php
```

**4. 数据库操作**
```bash
# 查看 mysql-prod 容器的 root 密码
docker inspect mysql-prod | grep MYSQL_ROOT_PASSWORD

# 创建数据库和用户 (使用获取到的密码)
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "CREATE DATABASE IF NOT EXISTS \`icbc_zjgx\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin; CREATE DATABASE IF NOT EXISTS \`icbc_zjgx_log\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin; CREATE USER IF NOT EXISTS 'icbc_zjgx'@'%' IDENTIFIED BY '21DCBA30DFFF959E'; GRANT ALL PRIVILEGES ON \`icbc_zjgx\`.* TO 'icbc_zjgx'@'%'; GRANT ALL PRIVILEGES ON \`icbc_zjgx_log\`.* TO 'icbc_zjgx'@'%'; FLUSH PRIVILEGES;"
```

**5. 调整目录权限**
```bash
chmod -R a+w /mnt/datadisk0/volumns/icbc/zjgx/web/runtime /mnt/datadisk0/volumns/icbc/zjgx/web/icbc /mnt/datadisk0/volumns/icbc/zjgx/web/application/icbc/service/sftp_temp
```

**6. 修改 Jssdk.php**
```bash
JSSDK_FILE=/mnt/datadisk0/volumns/icbc/zjgx/web/application/icbc/home/<USER>
cp $JSSDK_FILE $JSSDK_FILE.bak && \
sed -i 's/return json_success($sdkJson);/return json_success([]);/g' $JSSDK_FILE && \
sed -i '/\$sdkJson=\$js->buildConfig(\$APIs, \$debug = false, \$beta = false, \$json = true);/d' $JSSDK_FILE
```

查看web容器日志
```bash
docker exec -it icbc-zjgx-web tail -f /var/log/apache2/access.log
docker exec -it icbc-zjgx-web tail -f /var/log/apache2/error.log
```