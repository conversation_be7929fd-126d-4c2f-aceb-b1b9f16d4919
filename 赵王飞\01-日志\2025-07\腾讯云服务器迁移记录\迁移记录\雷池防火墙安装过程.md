

ubuntu@VM-0-17-ubuntu:~$ sudo bash -c "$(curl -fsSLk https://waf-ce.chaitin.cn/release/latest/manager.sh)"
  ______               ___           _____       _                        ____      ____       _        ________
.' ____ \            .' ..]         |_   _|     (_)                      |_  _|    |_  _|     / \      |_   __  |
| (___ \_|  ,--.    _| |_    .---.    | |       __    _ .--.    .---.      \ \  /\  / /      / _ \       | |_ \_|
 _.____`.  `'_\ :  '-| |-'  / /__\\   | |   _  [  |  [ `.-. |  / /__\\      \ \/  \/ /      / ___ \      |  _|
| \____) | // | |,   | |    | \__.,  _| |__/ |  | |   | | | |  | \__.,       \  /\  /     _/ /   \ \_   _| |_
 \______.' \'-;__/  [___]    '.__.' |________| [___] [___||__]  '.__.'        \/  \/     |____| |____| |_____|

[INFO  13:33:18]: SafeLine，中文名 "雷池"，是一款简单好用, 效果突出的 Web 应用防火墙(WAF)，可以保护 Web 服务不受黑客攻击。
[INFO  13:33:18]: 雷池通过过滤和监控 Web 应用与互 联网之间的 HTTP 流量来保护 Web 服务。可以保护 Web 服务免受 SQL 注入、XSS 、 代码注入、命令注入、CRLF 注入、ldap 注入、xpath 注入、RCE、XXE、SSRF、路径遍历、后门、暴力破解、CC、爬虫 等攻击。

选择你要执行的动作  [ 1.安装  2.升级  3.卸载  4.修复  5.重启 ]  (1/2/3/4/5): 1
[INFO  13:33:21]: 即将为您安装雷池 WAF
[INFO  13:33:21]: 检查 docker 版本
[INFO  13:33:21]: 检查 docker compose 版本
[INFO  13:33:21]: 检查安装环境已完成
请输入雷池 WAF 的安装目录  (留空则为默认值 /data/safeline): /opt/safeline
[INFO  13:33:39]: 正在初始化挂载目录
[INFO  13:33:39]: 挂载目录初始化完成
[INFO  13:33:39]: "/opt/safeline" 路径有 39.48 GB 的空间可用
[INFO  13:33:39]: 正在下载 docker-compose.yaml 文件
[INFO  13:33:39]: 正在更新 .env 配置文件
[INFO  13:34:10]: 正在获取雷池 WAF 最新版本
[INFO  13:34:10]: 目标版本：9.0.4
[INFO  13:34:10]: 正在拉取 Docker 镜像
[+] Pulling 72/72
 ✔ postgres Pulled                                                                                                                                                                                                                                                            20.5s 
 ✔ tengine Pulled                                                                                                                                                                                                                                                             38.4s 
 ✔ chaos Pulled                                                                                                                                                                                                                                                               41.6s 
 ✔ mgt Pulled                                                                                                                                                                                                                                                                 21.7s 
 ✔ luigi Pulled                                                                                                                                                                                                                                                                4.8s 
 ✔ fvm Pulled                                                                                                                                                                                                                                                                 55.3s 
 ✔ detect Pulled                                                                                                                                                                                                                                                              28.8s 
[INFO  13:35:05]: 正在启动 Docker 容器
[INFO  13:35:09]: 雷池 WAF 安装完成
[INFO  13:35:09]: 等待 mgt 启动
[INFO  13:35:14]: 等待 mgt 启动
[INFO  13:35:19]: 等待 mgt 启动
[INFO  13:35:24]: 等待 mgt 启动
[INFO  13:35:29]: 等待 mgt 启动
[INFO  13:35:34]: 等待 mgt 启动
[INFO  13:35:39]: 等待 mgt 启动
[INFO  13:35:44]: 设置 admin
[INFO  13:35:47]: 
[INFO] Initial username：admin
[INFO] Initial password：xpCee6Ds
[INFO] Done
[INFO  13:35:47]: 雷池 WAF 管理面板: https://172.17.0.17:9443/
[INFO  13:35:47]: 雷池 WAF 管理面板: https://111.229.155.148:9443/

▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄▄
█ ▄▄▄▄▄ █▀ █▀▀██▀▄▀▀▄▀▄▀▄██ ▄▄▄▄▄ █
█ █   █ █▀ ▄ █▀▄▄▀▀ ▄█▄  ▀█ █   █ █
█ █▄▄▄█ █▀█ █▄█▄▀▀▄▀▄ ▀▀▄▄█ █▄▄▄█ █
█▄▄▄▄▄▄▄█▄█▄█ █▄▀ █ ▀▄▀ █▄█▄▄▄▄▄▄▄█
█▄ ▄▄ █▄▄  ▄█▄▄▄▄▀▄▀▀▄██ ▄▄▀▄█▄▀ ▀█
█▄ ▄▀▄ ▄▀▄ ▀ ▄█▀ ▀▄ █▀▀ ▀█▀▄██▄▀▄██
██ ▀▄█ ▄ ▄▄▀▄▀▀█▄▀▄▄▀▄▀▄ ▄ ▀▄▄▄█▀▀█
█ █▀▄▀ ▄▀▄▄▀█▀ ▄▄ █▄█▀▀▄▀▀█▄█▄█▀▄██
█ █ ▀  ▄▀▀ ██▄█▄▄▄▄▄▀▄▀▀▀▄▄▀█▄▀█ ▀█
█ █ ▀▄ ▄██▀▀ ▄█▀ ▀███▄  ▀▄▀▄▄ ▄▀▄██
█▀▄▄█  ▄▀▄▀ ▄▀▀▀▄▀▄▀ ▄▀▄  ▄▀ ▄▀█ ▀█
█ █ █ █▄▀ █▄█▀ ▄▄███▀▀▀▄█▀▄ ▀  ▀▄██
█▄███▄█▄▄▀▄ █▄█▄▄▄▄▀▀▄█▀▀ ▄▄▄  ▀█ █
█ ▄▄▄▄▄ █▄▀█ ▄█▀▄ █▀█▄ ▀  █▄█  ▀▄▀█
█ █   █ █  █▄▀▀▀▄▄▄▀▀▀▀▀▀ ▄▄  ▀█  █
█ █▄▄▄█ █  ▀█▀ ▄▄▄▄ ▀█ ▀▀▄▀ ▀▀ ▀███
█▄▄▄▄▄▄▄█▄▄██▄█▄▄█▄██▄██▄▄█▄▄█▄█▄██

微信扫描上方二维码加入雷池项目讨论组
