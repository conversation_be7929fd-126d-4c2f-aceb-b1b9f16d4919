# VPN连接后内网访问指南

## 网络架构概览

### 服务器网络配置
- **公网IP**: ***************
- **主机内网IP**: *********** (eth0接口)
- **Docker网络**: **********/16 (1panel-network)

### VPN网络配置
- **VPN服务器**: OpenVPN Access Server
- **VPN网络段**: **********/16
- **客户端连接后获得的IP**: 172.20.0.x (动态分配)

## 关键服务IP地址

### 1. MySQL数据库
- **容器名**: 1Panel-mysql-h9Cu
- **内网IP**: **********
- **端口**: 3306
- **访问方式**: 
  - 从VPN客户端: `**********:3306`
  - 本地端口映射: `127.0.0.1:3306` (仅服务器本地)

### 2. OpenVPN Access Server
- **容器内网IP**: **********
- **管理界面**: https://**********:943
- **客户端下载**: https://**********:443

### 3. 其他Docker服务
所有在1panel-network网络中的服务都使用172.20.0.x网段

## VPN连接后的访问方法

### 连接VPN后你将获得：
1. **VPN客户端IP**: 172.20.0.x (例如 ***********)
2. **访问权限**: 可以直接访问**********/16网段内的所有服务

### 数据库连接示例：

#### 方法1: 直接连接MySQL容器IP
```bash
# 数据库连接参数
Host: **********
Port: 3306
Username: [你的数据库用户名]
Password: [你的数据库密码]
```

#### 方法2: 通过服务器主机IP (如果有端口映射)
```bash
# 如果MySQL有端口映射到主机
Host: ***********
Port: 3306
```

### 其他服务访问：

#### 1Panel管理面板 (如果部署了)
```bash
# 查找1Panel容器IP
sudo docker ps | grep 1panel
# 然后通过容器IP访问，例如:
http://172.20.0.x:8080
```

#### Web服务
```bash
# 如果有Web服务容器
http://172.20.0.x:端口号
```

## 网络测试命令

### 连接VPN后测试网络连通性：

```bash
# Windows客户端测试命令
# 1. 查看获得的VPN IP
ipconfig

# 2. 测试到MySQL服务器的连通性
ping **********
telnet ********** 3306

# 3. 测试到OpenVPN管理界面的连通性
ping **********

# 4. 测试到服务器主机的连通性
ping ***********
```

### Linux/Mac客户端测试命令：
```bash
# 1. 查看获得的VPN IP
ifconfig tun0

# 2. 测试MySQL连接
nc -zv ********** 3306

# 3. 使用MySQL客户端连接
mysql -h ********** -P 3306 -u username -p
```

## 重要注意事项

### 1. 网络段说明
- ************/12**: 服务器主机网络
- ************/16**: Docker容器网络 (也是VPN客户端网络)
- VPN客户端连接后会被分配到**********/16网段

### 2. 防火墙配置
OpenVPN Access Server已自动配置了必要的路由和防火墙规则，允许VPN客户端访问**********/16网段。

### 3. 安全建议
- 数据库连接建议使用SSL/TLS加密
- 定期更换VPN用户密码
- 监控VPN连接日志

### 4. 故障排除
如果无法访问内网服务：

1. **检查VPN连接状态**
   ```bash
   # Windows
   ipconfig | findstr "172.20"
   
   # Linux/Mac
   ifconfig | grep "172.20"
   ```

2. **检查路由表**
   ```bash
   # Windows
   route print | findstr "172.20"
   
   # Linux/Mac
   route -n | grep "172.20"
   ```

3. **测试网络连通性**
   ```bash
   ping **********  # 网关
   ping **********  # MySQL
   ping **********  # OpenVPN
   ```

## 常用连接字符串示例

### MySQL连接字符串
```
# JDBC
jdbc:mysql://**********:3306/database_name

# Python
mysql://username:password@**********:3306/database_name

# Node.js
{
  host: '**********',
  port: 3306,
  user: 'username',
  password: 'password',
  database: 'database_name'
}
```

### Redis连接 (如果有Redis服务)
```
redis://172.20.0.x:6379
```

通过VPN连接后，你就可以像在内网一样直接访问所有Docker容器服务了！---
date_created: 2025-07-17 13:33:05
date_modified: 2025-07-17 13:33:17
author: 赵王飞
---
