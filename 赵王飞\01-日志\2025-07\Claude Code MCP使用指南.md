# Claude Code MCP 使用指南手册

## 1. 概述

### 1.1 什么是MCP？
Model Context Protocol (MCP) 是Anthropic开发的开放协议，允许语言模型访问外部工具和数据源。通过MCP，Claude Code可以连接到远程服务器、数据库、文件系统等外部资源。

### 1.2 MCP的主要优势
- **扩展能力**: 让Claude能够访问外部系统和数据
- **标准化**: 统一的协议标准，支持多种服务类型
- **安全性**: 支持OAuth 2.0认证和权限控制
- **灵活性**: 支持stdio、SSE、HTTP多种服务器类型

## 2. 安装与配置

### 2.1 基本配置方法

#### 命令行配置（推荐）
```bash
# 基本语法
claude mcp add <服务器名称> [选项] -- <命令> [参数...]

# 示例：添加文件系统服务器
claude mcp add filesystem -s user -- npx -y @modelcontextprotocol/server-filesystem ~/Documents

# 示例：添加GitHub服务器
claude mcp add github -s user -e GITHUB_TOKEN=your-token -- npx -y @modelcontextprotocol/server-github
```

#### 配置选项说明
- `-s user`: 用户级别配置（所有项目共享）
- `-s project`: 项目级别配置（仅当前项目）
- `-s local`: 本地配置（当前会话）
- `-e KEY=VALUE`: 设置环境变量

### 2.2 配置文件方式

配置文件位置：`~/.claude.json`

#### Linux/macOS 配置格式：
```json
{
  "mcpServers": {
    "filesystem": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-filesystem", "/path/to/directory"],
      "env": {}
    },
    "github": {
      "type": "stdio",
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_TOKEN": "your-github-token"
      }
    }
  }
}
```

#### Windows 配置格式：
⚠️ **重要**: Windows系统需要使用 `cmd /c` 包装器来执行npx命令

```json
{
  "mcpServers": {
    "filesystem": {
      "type": "stdio",
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-filesystem", "/path/to/directory"],
      "env": {}
    },
    "github": {
      "type": "stdio",
      "command": "cmd",
      "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-github"],
      "env": {
        "GITHUB_TOKEN": "your-github-token"
      }
    }
  }
}
```

## 3. SSH MCP服务器配置

### 3.1 SSH MCP服务器简介
SSH MCP服务器允许Claude Code通过SSH协议连接到远程服务器，进行文件操作、命令执行等操作。

### 3.2 安装SSH MCP服务器

#### 方法一：命令行安装
```bash
claude mcp add mcp-ssh-tencent -s user -- npx -y @fangjunjie/ssh-mcp-server --host *************** --port 22 --username ubuntu --password "*uuXf6!t2Y97g2Kki2"
```

#### 方法二：配置文件安装

##### Linux/macOS 配置：
在 `~/.claude.json` 中添加：
```json
{
  "mcpServers": {
    "mcp-ssh-tencent": {
      "command": "npx",
      "args": [
        "-y",
        "@fangjunjie/ssh-mcp-server",
        "--host",
        "***************",
        "--port",
        "22",
        "--username",
        "ubuntu",
        "--password",
        "*uuXf6!t2Y97g2Kki2"
      ]
    }
  }
}
```

##### Windows 配置：
⚠️ **重要**: Windows系统需要使用 `cmd /c` 包装器
```json
{
  "mcpServers": {
    "mcp-ssh-tencent": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@fangjunjie/ssh-mcp-server",
        "--host",
        "***************",
        "--port",
        "22",
        "--username",
        "ubuntu",
        "--password",
        "*uuXf6!t2Y97g2Kki2"
      ]
    }
  }
}
```

### 3.3 SSH连接参数说明
- `--host`: SSH服务器IP地址或域名
- `--port`: SSH端口（默认22）
- `--username`: SSH用户名
- `--password`: SSH密码（不推荐，建议使用密钥认证）
- `--key`: SSH私钥文件路径
- `--passphrase`: 私钥密码

### 3.4 安全性考虑
⚠️ **重要提醒**：
- 避免在配置文件中明文存储密码
- 推荐使用SSH密钥认证
- 限制SSH用户权限
- 使用非标准端口
- 启用防火墙规则

## 4. 常用MCP服务器

### 4.1 文件系统服务器
```bash
# 基本配置
claude mcp add filesystem -s user -- npx -y @modelcontextprotocol/server-filesystem ~/Documents ~/Projects

# 多目录配置
claude mcp add filesystem -s user -- npx -y @modelcontextprotocol/server-filesystem ~/Documents ~/Projects ~/Desktop
```

### 4.2 GitHub服务器
```bash
# 需要GitHub Token
claude mcp add github -s user -e GITHUB_TOKEN=ghp_xxxxxxxxxxxx -- npx -y @modelcontextprotocol/server-github
```

### 4.3 数据库服务器
```bash
# PostgreSQL
claude mcp add postgres -s user -e DATABASE_URL=********************************/db -- npx -y @modelcontextprotocol/server-postgres

# MySQL
claude mcp add mysql -s user -e DATABASE_URL=mysql://user:pass@host:3306/db -- npx -y @modelcontextprotocol/server-mysql
```

### 4.4 Web搜索服务器
```bash
claude mcp add brave-search -s user -e BRAVE_API_KEY=your-api-key -- npx -y @modelcontextprotocol/server-brave-search
```

## 5. 使用方法

### 5.1 基本命令
```bash
# 查看已安装的MCP服务器
claude mcp list

# 查看MCP状态
/mcp

# 移除MCP服务器
claude mcp remove <服务器名称>
```

### 5.2 在对话中使用MCP资源
```bash
# 引用外部资源
@server:protocol://resource/path

# 示例：引用文件
@filesystem:file:///path/to/file.txt

# 示例：引用GitHub仓库
@github:repo://owner/repository
```

### 5.3 MCP斜杠命令
MCP服务器可以暴露提示作为斜杠命令：
```bash
# 格式
/mcp__servername__promptname

# 示例
/mcp__github__create_issue
```

## 6. 故障排除

### 6.1 常见错误

#### 错误1：Windows系统MCP服务器连接失败
```
错误信息: Windows requires 'cmd /c' wrapper to execute npx

解决方案：
1. 修改配置文件中的command从"npx"改为"cmd"
2. 在args数组开头添加"/c"参数
3. 格式示例：
   "command": "cmd",
   "args": ["/c", "npx", "-y", "服务器包名", "其他参数..."]
```

#### 错误2：服务器连接失败
```
解决方案：
1. 检查网络连接
2. 验证服务器地址和端口
3. 确认认证信息正确
4. 检查防火墙设置
```

#### 错误3：认证失败
```
解决方案：
1. 验证用户名和密码
2. 检查SSH密钥权限
3. 确认服务器允许密码/密钥认证
4. 查看SSH服务器日志
```

#### 错误4：权限不足
```
解决方案：
1. 检查用户权限
2. 确认目录访问权限
3. 使用sudo或管理员用户
4. 修改文件/目录权限
```

### 6.2 调试方法

#### 启用调试模式
```bash
# 启用调试输出
claude --debug

# 查看详细日志
claude --verbose
```

#### 检查MCP服务器状态
```bash
# 查看所有MCP服务器
/mcp

# 测试特定服务器
/mcp test server-name
```

### 6.3 日志位置
- Windows: `%APPDATA%\claude\logs\`
- macOS: `~/Library/Logs/claude/`
- Linux: `~/.local/share/claude/logs/`

## 7. 最佳实践

### 7.1 安全配置
1. **使用密钥认证**：避免密码认证
2. **限制权限**：最小权限原则
3. **定期更新**：保持MCP服务器最新版本
4. **监控访问**：记录和监控MCP活动

### 7.2 性能优化
1. **选择合适的作用域**：用户级vs项目级
2. **缓存配置**：合理配置缓存策略
3. **连接池**：对于数据库连接使用连接池
4. **超时设置**：合理设置连接和操作超时

### 7.3 开发建议
1. **测试环境**：先在测试环境验证配置
2. **版本控制**：将MCP配置纳入版本控制
3. **文档记录**：记录自定义MCP服务器配置
4. **团队共享**：标准化团队MCP配置

## 8. 高级功能

### 8.1 自定义MCP服务器
```javascript
// 示例：简单的MCP服务器
const { Server } = require('@modelcontextprotocol/sdk/server/index.js');
const { StdioServerTransport } = require('@modelcontextprotocol/sdk/server/stdio.js');

const server = new Server(
  {
    name: 'custom-server',
    version: '1.0.0',
  },
  {
    capabilities: {
      resources: {},
      tools: {},
    },
  }
);

// 实现具体功能...
```

### 8.2 远程MCP服务器
```json
{
  "mcpServers": {
    "remote-server": {
      "type": "sse",
      "url": "https://api.example.com/mcp",
      "auth": {
        "type": "oauth2",
        "clientId": "your-client-id"
      }
    }
  }
}
```

## 9. 附录

### 9.1 MCP服务器列表
- **@modelcontextprotocol/server-filesystem**: 文件系统访问
- **@modelcontextprotocol/server-github**: GitHub集成
- **@modelcontextprotocol/server-postgres**: PostgreSQL数据库
- **@modelcontextprotocol/server-mysql**: MySQL数据库
- **@modelcontextprotocol/server-brave-search**: Brave搜索
- **@fangjunjie/ssh-mcp-server**: SSH远程连接

### 9.2 参考链接
- [MCP官方文档](https://modelcontextprotocol.io/)
- [Claude Code文档](https://docs.anthropic.com/claude-code)
- [MCP GitHub仓库](https://github.com/modelcontextprotocol)

### 9.3 版本信息
- 文档版本: 1.0
- 更新日期: 2025-07-25
- 适用版本: Claude Code 最新版

---

**注意**: 使用第三方MCP服务器时请注意安全风险，仅使用可信来源的服务器。