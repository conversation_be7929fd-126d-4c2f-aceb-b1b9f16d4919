---
date_created: 2025-07-17 08:45:13
date_modified: 2025-07-17 08:45:14
author: 赵王飞
---




## 安装1panel 
[1Panel - 现代化、开源的Linux服务器运维管理面板](https://1panel.cn/)

### 安装命令
``` 
bash -c "$(curl -sSL https://resource.fit2cloud.com/1panel/package/v2/quick_start.sh)"
```
发现安装前需要先安装Docker

## 安装docker
[[腾讯云docer安装记录]]
```
[1Panel Log]: ======================= 开始安装 ======================= 
设置 1Panel 安装目录 (默认为 /opt): 
[1Panel Log]: 您选择的安装路径是 /opt 
[1Panel Log]: 检测到服务器 Docker 版本低于 20.x，建议手动升级以避免功能受限。 
```
从上述信息来看，docker 版本是存在问题的
```
[root@VM-0-17-tencentos tmp]# docker -v
Docker version 0.0.0-20241223130549-3b49deb, build 3b49deb
```
