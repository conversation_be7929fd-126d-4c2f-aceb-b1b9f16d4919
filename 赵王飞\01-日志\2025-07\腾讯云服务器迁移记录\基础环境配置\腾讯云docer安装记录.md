# 需要安装 docer

[Docker 容器用户使用指南](https://cloud.tencent.com/document/product/1397/110965)
```
sudo yum install -y docker-ce
```
安装完成后查看版本
```
sudo docker info
Client:
 Version:    0.0.0-20241223130549-3b49deb
 Context:    default
 Debug Mode: false
 Plugins:
  buildx: Docker Buildx (Docker Inc.)
    Version:  %{_buildx_version}
    Path:     /usr/libexec/docker/cli-plugins/docker-buildx
  compose: Docker Compose (Docker Inc.)
    Version:  2.32.1
    Path:     /usr/libexec/docker/cli-plugins/docker-compose

Server:
 Containers: 0
  Running: 0
  Paused: 0
  Stopped: 0
 Images: 0
 Server Version: 28.0.1
 Storage Driver: overlay2
  Backing Filesystem: xfs
  Supports d_type: true
  Using metacopy: true
  Native Overlay Diff: false
  userxattr: false
 Logging Driver: json-file
 Cgroup Driver: systemd
 Cgroup Version: 2
 Plugins:
  Volume: local
  Network: bridge host ipvlan macvlan null overlay
  Log: awslogs fluentd gcplogs gelf journald json-file local splunk syslog
 Swarm: inactive
 Runtimes: io.containerd.runc.v2 runc
 Default Runtime: runc
 Init Binary: docker-init
 containerd version: 
 runc version: 
 init version: de40ad0
 Security Options:
  seccomp
   Profile: builtin
  cgroupns
 Kernel Version: 6.6.92-34.1.tl4.x86_64
 Operating System: TencentOS Server 4.4
 OSType: linux
 Architecture: x86_64
 CPUs: 8
 Total Memory: 30.97GiB
 Name: VM-0-17-tencentos
 ID: 020cc5ef-a1ab-401d-aee3-a2ff51cd9479
 Docker Root Dir: /var/lib/docker
 Debug Mode: false
 Experimental: false
 Insecure Registries:
  ::1/128
  *********/8
 Live Restore Enabled: false
```

在另一篇文章中[搭建 Docker](https://cloud.tencent.com/document/product/213/46000?cps_key=1d358d18a7a17b4a6df8d67a62fd3d3d)发现安装命令和这个不一样

```
sudo yum install docker -y
```

## 测试服务器出口 IP
要查询服务器出口IP地址，有以下几种方法： 使用命令行工具（如cmd或Terminal）：**打开命令行工具，输入"nslookup myip.opendns.com resolver1.opendns.com"，然后回车**。 系统会自动返回你的服务器出口IP地址。
要测试服务器的出口IP 地址，==可以使用命令行工具或在线服务==。在Linux 服务器上，可以使用 `curl ifconfig.me/ip` 或 `curl ipinfo.io/ip` 命令来获取公网IP 地址。在Windows 服务器上，可以使用 `ipconfig` 命令来查看，或者使用在线服务如 `ip.cn`

## [TencentOS Server 产品概述](https://cloud.tencent.com/document/product/1397)

TencentOS Server 是腾讯云针对云的场景研发的 Linux 操作系统，提供特定的功能及性能优化，为云服务器实例中的应用程序提供更高的性能及更加安全可靠的运行环境。TencentOS Server 基于 Linux 内核自主研发设计，积累了腾讯在操作系统领域超过10年的技术积累，并经过了腾讯内部海量业务多年验证和打磨，在腾讯内部操作系统里占比超99%，覆盖了腾讯所有的业务。同时，腾讯有着国内最种类繁多的业务生态，从社交、游戏、金融支付、AI、安全等，其稳定性、安全性、兼容性和性能等核心能力均已得到长时间充分验证，相比社区 OS 版本，TencentOS Server 在稳定性、性能、容器基础设施等核心能力方面做了全面的增强和优化，能为企业提供稳定高可用的服务，满足业务严苛负载需求，力求打造云上最佳操作系统，更佳的企业级操作系统解决方案。
### TencentOS Server 镜像版本
目前腾讯云上有3款 TencentOS Server 镜像供用户选择：


| 镜像版本                    | 说明                                                                         |
| ----------------------- | -------------------------------------------------------------------------- |
| TencentOS Server 4（TK5） | 内置 Linux 6.6 LTS 稳定版本内核，其内核及用户态软件均基于 upstream 社区独立演进，自主选型和维护，不再依赖任何第三方发行版。 |
| TencentOS Server 3（TK4） | 与 CentOS 8用户态完全兼容，配套基于社区5.4 LTS 内核深度优化的 tkernel4 版本。                       |
| TencentOS Server 2（TK4） | 与 CentOS 7用户态完全兼容，配套基于社区5.4 LTS 内核深度优化的 tkernel4 版本。                       |
### TencentOS Server 内核

TencentOS Server 2：当前内核是基于社区 Linux 5.4 LTS 深度优化的 tkernel4（简称 tk4）。
TencentOS Server 3：当前内核是基于社区 Linux 5.4 LTS 深度优化的 tkernel4（简称 tk4）。
TencentOS Server 4 ：当前内核是基于社区 Linux 6.6 LTS 稳定版本内核（简称 tk5）。


  
