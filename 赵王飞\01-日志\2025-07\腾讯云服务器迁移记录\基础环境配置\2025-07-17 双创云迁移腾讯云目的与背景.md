---
date_created: 2025-07-17 09:15:59
date_modified: 2025-07-17 09:15:59
author: 赵王飞
---

运行了好几年的双创云服务器终于停服, 对于我们来说，下一步需要去购买公有云服务器，然后将原有的双创云相关的业务迁移到公有云上.
目前考虑尽可能的压缩公有云的服务器使用成本, 所以我再再考虑使用 k8s, 因为 k8s 每一个节点都会占用两到3g 内存用来维护 k8s 相关核心.

# 任务背景

# 服务器概况
## 配置信息
| 配置项       | 规格详情                     |
|--------------|-----------------------------|
| 操作系统     | TencentOS Server 4 for x86_64 |
| CPU          | 8核                         |
| 系统盘       | 50GiB 高性能云硬盘          |
| 内存         | 32GB                        |
| 公网带宽     | 5Mbps                       |
| 数据盘       | 1块 500GiB 高性能云硬盘     |

## 操作系统概况
腾讯云服务器默认选择的操作系统是TencentOS Server,而我当前使用的是TencentOS Server 4
安装完成后查看内存占用
```
[root@VM-0-17-tencentos ~]# free -h
               total        used        free      shared  buff/cache   available
Mem:            30Gi       673Mi        30Gi        11Mi       615Mi        30Gi
Swap:             0B          0B          0B

[root@VM-0-17-tencentos ~]# apt-get
-bash: apt-get: command not found
```
这是并非使用的ubuntu

```
[root@VM-0-17-tencentos ~]# df -h
Filesystem      Size  Used Avail Use% Mounted on
/dev/vda1        50G  3.7G   47G   8% /
devtmpfs        4.0M     0  4.0M   0% /dev
tmpfs            16G   24K   16G   1% /dev/shm
tmpfs           6.2G  9.6M  6.2G   1% /run
/dev/vdb        492G  2.1M  467G   1% /mnt/datadisk0
```

# 架构计划
原来的业务大部分通过 docker 和 k8s 实现，为了抹平服务器的系统差异，考虑新服务器以 docker 为主要业务提供.
又考虑到公司的运营人员都是非专职运营人员, 所以需要一个管理平台, 考虑使用1PANEL.





