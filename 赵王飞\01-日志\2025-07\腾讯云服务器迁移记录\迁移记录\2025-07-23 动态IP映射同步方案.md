---
date_created: 2025-07-23 11:45:00
date_modified: 2025-07-23 11:45:00
author: Cascade
tags: [Docker, IP映射, 自动化, 简化方案]
related_docs: ["2025-07-23 本地通过容器名称访问服务器问题解决.md", "2025-07-23 统一固定IP网络方案.md"]
---

# 动态IP映射同步方案

## 方案背景

经过对复杂网络方案（DNS桥接、固定IP网络）的深入探索和验证，我们发现了根本性的技术障碍：

1. **OpenVPN路由限制**：VPN客户端只能访问到OpenVPN容器本身，无法访问服务器宿主机或其他容器
2. **网络隔离问题**：即使在同一Docker网络中，容器间的路由转发也存在复杂的技术门槛
3. **复杂性与可靠性矛盾**：复杂的网络配置带来了更多的故障点和维护难度

因此，我们决定回归**最简单、最直接、最可靠**的解决方案：**动态IP映射同步**。

## 方案核心思路

### 基本原理
1. **服务器端监控**：定期检测Docker容器的IP地址变化
2. **映射文件生成**：自动生成hosts文件格式的IP映射条目
3. **客户端同步**：通过简单可靠的方式将映射信息同步到客户端
4. **自动更新**：客户端自动更新本地hosts文件

### 技术优势
- ✅ **简单可靠**：无需复杂的网络配置，直接解决问题根源
- ✅ **完全绕过网络限制**：不依赖VPN路由转发能力
- ✅ **自动化程度高**：一次配置，长期自动运行
- ✅ **故障点少**：技术栈简单，问题排查容易
- ✅ **扩展性好**：新增容器只需更新配置文件

## 实施方案

### 服务器端组件

#### 1. IP监控脚本
```bash
#!/bin/bash
# docker-ip-monitor.sh - Docker容器IP监控脚本

# 配置文件
CONFIG_FILE="/opt/docker-ip-monitor/containers.conf"
OUTPUT_FILE="/opt/docker-ip-monitor/hosts-mapping.txt"
LOG_FILE="/opt/docker-ip-monitor/monitor.log"

# 日志函数
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 获取容器IP
get_container_ip() {
    local container_name=$1
    docker inspect "$container_name" 2>/dev/null | \
        jq -r '.[0].NetworkSettings.Networks["1panel-network"].IPAddress // empty'
}

# 生成hosts映射
generate_hosts_mapping() {
    local temp_file=$(mktemp)
    local changed=false
    
    log "开始检测容器IP变化..."
    
    # 读取配置文件中的容器列表
    while IFS= read -r container_name; do
        [[ "$container_name" =~ ^#.*$ ]] && continue  # 跳过注释行
        [[ -z "$container_name" ]] && continue       # 跳过空行
        
        local current_ip=$(get_container_ip "$container_name")
        
        if [[ -n "$current_ip" ]]; then
            echo "$current_ip $container_name" >> "$temp_file"
            log "检测到容器: $container_name -> $current_ip"
        else
            log "警告: 无法获取容器 $container_name 的IP地址"
        fi
    done < "$CONFIG_FILE"
    
    # 检查是否有变化
    if [[ ! -f "$OUTPUT_FILE" ]] || ! cmp -s "$temp_file" "$OUTPUT_FILE"; then
        mv "$temp_file" "$OUTPUT_FILE"
        log "IP映射已更新: $OUTPUT_FILE"
        changed=true
    else
        rm "$temp_file"
        log "IP映射无变化"
    fi
    
    echo "$changed"
}

# 主函数
main() {
    log "Docker IP监控脚本启动"
    
    # 创建必要的目录
    mkdir -p "$(dirname "$CONFIG_FILE")"
    mkdir -p "$(dirname "$OUTPUT_FILE")"
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # 检查配置文件
    if [[ ! -f "$CONFIG_FILE" ]]; then
        log "错误: 配置文件不存在: $CONFIG_FILE"
        exit 1
    fi
    
    # 生成映射文件
    changed=$(generate_hosts_mapping)
    
    if [[ "$changed" == "true" ]]; then
        log "检测到IP变化，映射文件已更新"
        # 这里可以添加通知机制，如发送邮件、调用API等
    fi
    
    log "监控完成"
}

# 执行主函数
main "$@"
```

#### 2. 容器配置文件
```bash
# /opt/docker-ip-monitor/containers.conf
# Docker容器监控配置文件
# 每行一个容器名称，以#开头的行为注释

# 数据库容器
mysql-prod
mysql-test

# 消息队列
rabbitmq-prod

# Web应用
hyqm2-prod
icbc-chifeng-web
icbc-jtgf-web

# 桥接服务
icbc-naoer-bridge
icbc-jtgf-bridge
```

#### 3. 定时任务配置
```bash
# 添加到crontab
# 每5分钟检查一次容器IP变化
*/5 * * * * /opt/docker-ip-monitor/docker-ip-monitor.sh

# 每小时进行一次完整检查（可选）
0 * * * * /opt/docker-ip-monitor/docker-ip-monitor.sh --full-check
```

### 客户端同步机制

#### 方案A：HTTP API同步
```bash
#!/bin/bash
# client-sync.sh - 客户端同步脚本

SERVER_URL="http://your-server.com:8080/api/hosts-mapping"
LOCAL_HOSTS_FILE="/etc/hosts"
BACKUP_FILE="/etc/hosts.backup.$(date +%Y%m%d_%H%M%S)"
TEMP_FILE=$(mktemp)

# 下载最新映射
curl -s "$SERVER_URL" > "$TEMP_FILE"

if [[ $? -eq 0 ]] && [[ -s "$TEMP_FILE" ]]; then
    # 备份当前hosts文件
    cp "$LOCAL_HOSTS_FILE" "$BACKUP_FILE"
    
    # 移除旧的Docker映射条目
    sed -i '/# Docker容器IP映射/,/# Docker容器IP映射结束/d' "$LOCAL_HOSTS_FILE"
    
    # 添加新的映射条目
    echo "# Docker容器IP映射" >> "$LOCAL_HOSTS_FILE"
    cat "$TEMP_FILE" >> "$LOCAL_HOSTS_FILE"
    echo "# Docker容器IP映射结束" >> "$LOCAL_HOSTS_FILE"
    
    echo "Hosts文件已更新"
else
    echo "同步失败"
fi

rm -f "$TEMP_FILE"
```

#### 方案B：文件共享同步
```bash
#!/bin/bash
# 通过共享目录同步（适用于VPN环境）

SHARED_MAPPING_FILE="/mnt/shared/docker-hosts-mapping.txt"
LOCAL_HOSTS_FILE="/etc/hosts"

if [[ -f "$SHARED_MAPPING_FILE" ]]; then
    # 检查文件是否有更新
    if [[ "$SHARED_MAPPING_FILE" -nt "$LOCAL_HOSTS_FILE.last_update" ]]; then
        # 更新hosts文件
        update_hosts_file "$SHARED_MAPPING_FILE"
        touch "$LOCAL_HOSTS_FILE.last_update"
        echo "Hosts文件已同步更新"
    fi
fi
```

### Windows客户端脚本

#### PowerShell同步脚本
```powershell
# docker-hosts-sync.ps1
param(
    [string]$ServerUrl = "http://your-server.com:8080/api/hosts-mapping",
    [string]$HostsFile = "$env:SystemRoot\System32\drivers\etc\hosts"
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Error "此脚本需要管理员权限运行"
    exit 1
}

try {
    # 下载最新映射
    $mapping = Invoke-RestMethod -Uri $ServerUrl -TimeoutSec 10
    
    # 备份hosts文件
    $backupFile = "$HostsFile.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $HostsFile $backupFile
    
    # 读取当前hosts文件
    $hostsContent = Get-Content $HostsFile
    
    # 移除旧的Docker映射
    $newContent = @()
    $inDockerSection = $false
    
    foreach ($line in $hostsContent) {
        if ($line -match "# Docker容器IP映射") {
            $inDockerSection = $true
            continue
        }
        if ($line -match "# Docker容器IP映射结束") {
            $inDockerSection = $false
            continue
        }
        if (-not $inDockerSection) {
            $newContent += $line
        }
    }
    
    # 添加新的映射
    $newContent += "# Docker容器IP映射"
    $newContent += $mapping -split "`n"
    $newContent += "# Docker容器IP映射结束"
    
    # 写入hosts文件
    $newContent | Out-File -FilePath $HostsFile -Encoding ASCII
    
    Write-Host "Hosts文件已更新" -ForegroundColor Green
    
} catch {
    Write-Error "同步失败: $($_.Exception.Message)"
}
```

## 部署指南

### 服务器端部署

1. **创建监控目录**
```bash
sudo mkdir -p /opt/docker-ip-monitor
sudo chown $USER:$USER /opt/docker-ip-monitor
```

2. **部署脚本和配置**
```bash
# 复制监控脚本
cp docker-ip-monitor.sh /opt/docker-ip-monitor/
chmod +x /opt/docker-ip-monitor/docker-ip-monitor.sh

# 创建配置文件
cp containers.conf /opt/docker-ip-monitor/
```

3. **配置定时任务**
```bash
# 编辑crontab
crontab -e

# 添加定时任务
*/5 * * * * /opt/docker-ip-monitor/docker-ip-monitor.sh
```

4. **配置HTTP服务（可选）**
```bash
# 使用nginx提供HTTP访问
sudo tee /etc/nginx/sites-available/docker-hosts > /dev/null <<EOF
server {
    listen 8080;
    server_name _;
    
    location /api/hosts-mapping {
        alias /opt/docker-ip-monitor/hosts-mapping.txt;
        add_header Content-Type text/plain;
    }
}
EOF

sudo ln -s /etc/nginx/sites-available/docker-hosts /etc/nginx/sites-enabled/
sudo systemctl reload nginx
```

### 客户端部署

#### Windows客户端
1. **下载PowerShell脚本**
2. **创建定时任务**
```powershell
# 创建计划任务（每10分钟执行一次）
$action = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-File C:\Scripts\docker-hosts-sync.ps1"
$trigger = New-ScheduledTaskTrigger -RepetitionInterval (New-TimeSpan -Minutes 10) -Once -At (Get-Date)
$principal = New-ScheduledTaskPrincipal -UserId "SYSTEM" -LogonType ServiceAccount -RunLevel Highest

Register-ScheduledTask -TaskName "DockerHostsSync" -Action $action -Trigger $trigger -Principal $principal
```

#### Linux客户端
```bash
# 添加到crontab
*/10 * * * * /opt/scripts/client-sync.sh
```

## 监控和维护

### 日志监控
```bash
# 查看监控日志
tail -f /opt/docker-ip-monitor/monitor.log

# 检查同步状态
grep "IP映射已更新" /opt/docker-ip-monitor/monitor.log
```

### 故障排除
1. **检查容器状态**
```bash
docker ps | grep -E "(mysql|rabbitmq|icbc|hyqm)"
```

2. **验证IP获取**
```bash
/opt/docker-ip-monitor/docker-ip-monitor.sh --test
```

3. **手动同步测试**
```bash
# 服务器端
cat /opt/docker-ip-monitor/hosts-mapping.txt

# 客户端
curl http://your-server:8080/api/hosts-mapping
```

## 扩展功能

### 1. 变化通知
```bash
# 在监控脚本中添加通知功能
send_notification() {
    local message=$1
    # 发送邮件
    echo "$message" | mail -s "Docker IP变化通知" <EMAIL>
    
    # 或者调用企业微信/钉钉API
    curl -X POST "https://api.weixin.qq.com/..." -d "{\"text\":\"$message\"}"
}
```

### 2. 健康检查
```bash
# 添加服务健康检查
check_service_health() {
    local container_name=$1
    local port=$2
    local ip=$(get_container_ip "$container_name")
    
    if [[ -n "$ip" ]]; then
        nc -z "$ip" "$port" && echo "OK" || echo "FAIL"
    fi
}
```

### 3. Web管理界面
```html
<!-- 简单的Web管理界面 -->
<!DOCTYPE html>
<html>
<head>
    <title>Docker容器IP监控</title>
</head>
<body>
    <h1>容器IP映射状态</h1>
    <div id="mapping-list"></div>
    
    <script>
        fetch('/api/hosts-mapping')
            .then(response => response.text())
            .then(data => {
                document.getElementById('mapping-list').innerHTML = 
                    '<pre>' + data + '</pre>';
            });
    </script>
</body>
</html>
```

## 方案优势总结

1. **技术简单**：基于成熟的hosts文件机制，无复杂网络配置
2. **可靠性高**：故障点少，问题容易定位和解决
3. **自动化程度高**：一次配置，长期自动运行
4. **扩展性好**：可轻松添加新容器或新客户端
5. **维护成本低**：无需专业网络知识，普通运维即可维护

---

**文档状态**：待实施  
**创建时间**：2025-07-23  
**预计完成时间**：2025-07-24  
**维护难度**：低
