# 服务端应用容器化管理规范

## 1. 总体架构设计

### 1.1 设计理念
基于Docker容器化技术，建立标准化的应用部署和管理体系，实现配置与数据分离、安全隔离、统一管理的现代化服务端架构。

### 1.2 核心原则
- **配置与数据分离**：应用配置与业务数据完全分离存储
- **安全优先**：最小化端口暴露，网络隔离，权限控制
- **标准化管理**：统一的目录结构、命名规范、管理工具
- **可扩展性**：支持多种应用类型的快速部署和扩展

## 2. 目录架构标准

### 2.1 根目录结构
```
/mnt/datadisk0/
├── apps/                       # 应用配置管理目录
│   ├── [应用类型]/             # 按应用类型分组
│   │   └── [应用实例]/         # 具体应用实例
│   └── shared/                 # 共享配置
└── volumns/                    # 数据存储目录
    ├── [应用类型]/             # 按应用类型分组
    │   └── [应用实例]/         # 应用数据
    └── shared/                 # 共享数据
```

### 2.2 应用配置目录标准
```
/mnt/datadisk0/apps/[应用类型]/[应用实例]/
├── conf/                       # 配置文件目录
│   ├── [服务名]/               # 按服务分组配置
│   └── shared/                 # 共享配置
├── logs/                       # 日志文件目录
├── scripts/                    # 脚本目录
│   ├── manage.sh              # 管理脚本
│   ├── backup.sh              # 备份脚本
│   └── deploy.sh              # 部署脚本
├── docker-compose.yml          # 容器编排配置
├── .env                       # 环境变量
└── README.md                  # 应用文档
```

### 2.3 数据存储目录标准
```
/mnt/datadisk0/volumns/[应用类型]/[应用实例]/
├── data/                       # 应用数据
├── uploads/                    # 上传文件
├── cache/                      # 缓存文件
└── temp/                       # 临时文件
```

## 3. 命名规范

### 3.1 目录命名
- **应用类型**：`php-web`, `java-app`, `python-api`, `node-service`
- **应用实例**：`[项目名]-[环境]`，如 `pearproject-prod`, `icbc-chifeng`
- **容器名称**：`[应用类型]-[实例名]`，如 `php-web-pearproject-prod`

### 3.2 网络命名
- **主网络**：`1panel-network` (已存在)
- **应用网络**：`[应用类型]-network`，如 `php-web-network`

### 3.3 卷命名
- **数据卷**：`[应用实例]-data`
- **配置卷**：`[应用实例]-config`
- **日志卷**：`[应用实例]-logs`

## 4. 容器配置标准

### 4.1 网络配置
```yaml
networks:
  1panel-network:
    external: true
  [应用类型]-network:
    driver: bridge
```

### 4.2 安全配置
- **端口策略**：禁止直接暴露端口到公网
- **网络隔离**：使用内部网络通信
- **权限控制**：最小权限原则
- **环境变量**：敏感信息通过.env文件管理

### 4.3 资源限制
```yaml
deploy:
  resources:
    limits:
      cpus: '1.0'
      memory: 1G
    reservations:
      cpus: '0.5'
      memory: 512M
```

## 5. 应用类型标准

### 5.1 PHP Web应用
```
apps/php-web/[实例名]/
├── conf/
│   ├── apache2/               # Apache配置
│   ├── nginx/                 # Nginx配置(可选)
│   └── php/                   # PHP配置
├── logs/
└── scripts/manage.sh
```

### 5.2 Java应用
```
apps/java-app/[实例名]/
├── conf/
│   ├── application.yml        # Spring配置
│   └── logback.xml           # 日志配置
├── logs/
└── scripts/manage.sh
```

### 5.3 Python应用
```
apps/python-api/[实例名]/
├── conf/
│   ├── settings.py           # Django/Flask配置
│   └── requirements.txt      # 依赖配置
├── logs/
└── scripts/manage.sh
```

### 5.4 数据库服务
```
apps/database/[实例名]/
├── conf/
│   ├── my.cnf               # MySQL配置
│   └── redis.conf           # Redis配置
├── logs/
└── scripts/manage.sh
```

## 6. 管理工具标准

### 6.1 管理脚本模板
每个应用必须包含标准的管理脚本，支持以下操作：
- `start` - 启动服务
- `stop` - 停止服务
- `restart` - 重启服务
- `status` - 查看状态
- `logs` - 查看日志
- `shell` - 进入容器
- `backup` - 备份数据
- `restore` - 恢复数据

### 6.2 监控标准
- **健康检查**：容器级别的健康检查
- **日志监控**：统一的日志格式和收集
- **性能监控**：资源使用情况监控
- **告警机制**：异常情况自动告警

## 7. 安全管理规范

### 7.1 网络安全
- 所有应用容器连接到`1panel-network`
- 禁止直接暴露端口到公网
- 通过1Panel OpenResty统一反向代理

### 7.2 文件安全
- 配置文件与数据文件分离
- 敏感配置通过环境变量管理
- 定期清理临时文件和日志

### 7.3 权限管理
- 容器内使用非root用户运行
- 文件权限最小化原则
- 定期审查权限配置

## 8. 备份与恢复

### 8.1 备份策略
- **配置备份**：apps目录定期备份
- **数据备份**：volumns目录定期备份
- **数据库备份**：独立的数据库备份策略
- **镜像备份**：关键镜像的备份和版本管理

### 8.2 恢复流程
1. 停止相关服务
2. 恢复配置文件
3. 恢复数据文件
4. 重新启动服务
5. 验证服务状态

## 9. 部署流程标准

### 9.1 新应用部署
1. 创建应用目录结构
2. 准备配置文件
3. 编写docker-compose.yml
4. 创建管理脚本
5. 部署数据文件
6. 启动容器服务
7. 配置反向代理
8. 验证服务可用性

### 9.2 应用更新
1. 备份当前配置和数据
2. 停止服务
3. 更新配置或镜像
4. 启动服务
5. 验证更新结果
6. 回滚机制(如需要)

## 10. 监控与维护

### 10.1 日常维护
- 定期检查容器状态
- 监控磁盘空间使用
- 清理过期日志文件
- 更新安全补丁

### 10.2 性能优化
- 监控资源使用情况
- 优化容器资源配置
- 调整网络配置
- 数据库性能调优

## 11. 扩展规划

### 11.1 水平扩展
- 支持多实例部署
- 负载均衡配置
- 服务发现机制

### 11.2 垂直扩展
- 资源配置调整
- 性能参数优化
- 缓存策略优化

## 12. 最佳实践

### 12.1 开发规范
- 使用标准化的Dockerfile
- 配置文件模板化
- 环境变量标准化
- 日志格式统一

### 12.2 运维规范
- 定期备份验证
- 监控告警配置
- 文档及时更新
- 变更记录管理

这套管理规范为服务端应用的容器化部署提供了完整的标准和指导，确保系统的安全性、可维护性和可扩展性。
