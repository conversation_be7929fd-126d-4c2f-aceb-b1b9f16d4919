
# 背景

服务器的局域网 IP为***********，内部容器之间需要相互访问，例如icbc-chifeng-web需要访问数据库mysql-prod（原1Panel-mysql-h9Cu），但是使用局域网 IP 无法访问联通，我该怎么办？你可以直接使用 ssh-mpc-server 插件连接遇到远程服务器进行获取. 不要在我本地 Windows 环境执行命令.

## 问题诊断过程

### 1. 查看容器状态
```bash
docker ps
```

发现关键容器：
- **icbc-chifeng-web**: 端口映射 0.0.0.0:20032->80/tcp
- **mysql-prod**（原1Panel-mysql-h9Cu): 端口映射 127.0.0.1:3306->3306/tcp

### 2. 检查网络配置
```bash
docker network ls
```

发现网络：
- **bridge**: 默认网络
- **1panel-network**: 1Panel创建的网络
- **safeline-ce**: 安全防护网络

### 3. 检查容器网络归属
```bash
# 检查icbc-chifeng-web网络配置
docker inspect icbc-chifeng-web | grep -A 10 -B 5 "Networks"

# 检查MySQL容器网络配置  
docker inspect 1Panel-mysql-h9Cu | grep -A 10 -B 5 "Networks"
```

**诊断结果**：
- **icbc-chifeng-web** 在 **bridge** 网络中，IP: `**********`
- **mysql-prod** 在 **1panel-network** 网络中，IP范围: `172.20.0.x`

## 问题根因

**容器网络隔离**：两个容器位于不同的Docker网络中，无法直接通信。

## 解决方案

### 方案一：将icbc-chifeng-web加入1panel-network网络（推荐）

#### 优势
- 操作简单，只需一个命令
- 保持数据库容器现有配置不变
- 兼容1Panel的网络管理

#### 实施命令
```bash
# 将icbc-chifeng-web容器连接到1panel-network网络
docker network connect 1panel-network icbc-chifeng-web

# 验证连接
docker inspect icbc-chifeng-web | grep -A 15 "Networks"

# 测试连通性
docker exec -it icbc-chifeng-web ping mysql-prod

# 可选：断开与bridge网络的连接
docker network disconnect bridge icbc-chifeng-web

# 重启容器确保配置生效
docker restart icbc-chifeng-web
```

### 方案二：创建自定义网络

#### 优势
- 更好的网络隔离和管理
- 独立于1Panel的网络配置

#### 实施命令
```bash
# 创建自定义网络
docker network create --driver bridge app-network

# 将两个容器都连接到新网络
docker network connect app-network icbc-chifeng-web
docker network connect app-network mysql-prod

# 重启容器
docker restart icbc-chifeng-web
docker restart mysql-prod
```

### 方案三：使用容器名称通信

#### 优势
- 最灵活的方案
- 便于后期维护
- 应用程序中直接使用容器名作为主机名

#### 配置说明
连接到同一网络后，在应用程序中使用以下配置：
- **数据库主机**: `mysql-prod`
- **端口**: `3306`

## 关于使用服务器IP访问数据库

### 问题
无法通过服务器局域网IP `***********:3306` 访问数据库。

### 原因
1. MySQL容器端口只绑定到 `127.0.0.1:3306`（本地回环）
2. 没有绑定到服务器的外部网络接口 `***********`

### 如需外部访问的解决方案
```bash
# 停止当前容器
docker stop mysql-prod

# 重新运行容器，绑定到服务器外部IP
docker run -d --name mysql-prod \
  --network 1panel-network \
  -p ***********:3306:3306 \
  -v [原有的数据卷映射] \
  mysql:5.7.44
```

## 推荐配置

对于容器间通信，推荐使用：
1. **方案一**：简单快速解决当前问题
2. **使用容器名称**：`mysql-prod` 作为数据库主机名
3. **保持内部访问**：避免暴露数据库端口到外部网络，提高安全性

## 验证命令

```bash
# 检查网络连接状态
docker network inspect 1panel-network

# 测试容器间连通性
docker exec -it icbc-chifeng-web ping mysql-prod

# 测试数据库连接（如果有mysql客户端）
docker exec -it icbc-chifeng-web mysql -h mysql-prod -u root -p
```

## 执行记录

### 选择原因
选择**方案一：将icbc-chifeng-web加入1panel-network网络**，因为：
- 操作简单，只需一个命令即可解决问题
- 保持数据库容器现有配置不变
- 兼容1Panel的网络管理，避免影响其他服务

### 实施内容
1. **执行方案一**：将icbc-chifeng-web容器连接到1panel-network网络
2. **数据库容器重命名**：将 `1Panel-mysql-h9Cu` 重命名为 `mysql-prod`

### 关键参数配置
- **网络连接命令**：`docker network connect 1panel-network icbc-chifeng-web`
- **新的数据库主机名**：`mysql-prod`
- **数据库端口**：`3306`（保持不变）
- **网络**：容器间通过1panel-network进行通信

### 实际执行后的访问方式
应用程序现在可以通过以下方式访问数据库：
- **数据库主机**：`mysql-prod`
- **端口**：`3306`
- **网络**：1panel-network内部通信

