# 汇远融媒服务迁移规划文档

## 1. 迁移概述

### 1.1 迁移目标
将汇远融媒业务从K8s集群迁移到腾讯云Docker容器化环境，实现：
- 从K8s部署模式转换为Docker Compose部署
- 从NFS共享存储转换为本地卷挂载
- 保持数据库连接到214服务器不变
- 实现统一的容器化管理

### 1.2 技术架构转换
```
原架构：K8s + NFS共享存储 + 214数据库
新架构：Docker + 本地卷挂载 + 腾讯云数据库
```

### 1.3 迁移范围确认
**确定需要迁移的服务**：
- ✅ `hyqm_prod` - 汇远轻媒主服务
- ✅ `hyqm_qm` - 汇远轻媒-无logo版本
- ✅ `hyrm_imppc` - 警察学院
- ✅ `hyrm_imusti` - 监察官学院
- 🔄 其他实例待您确认标记

## 2. 当前环境分析

### 2.1 K8s容器现状
通过208服务器kubectl查看，发现以下汇远融媒相关容器：

| 容器名称 | 镜像ID | 状态 | 说明 |
|---------|--------|------|------|
| `k8s_web-ruoyi-hyrm-imnu_*` | `4525e4716f8b` | Up 3 weeks | 内蒙古大学实例 |
| `k8s_web-ruoyi-hyrm-demo_*` | `4525e4716f8b` | Up 3 weeks | 演示实例 |

### 2.2 数据存储分析

#### NFS共享存储（210服务器）

**主要迁移目录：/data/nfs_share/html/HOME_PROD/**
```
/data/nfs_share/html/HOME_PROD/
├── hyqm_prod/         # 汇远轻媒主服务 【需要迁移】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyqm_qm/           # 汇远轻媒-无logo版本 【需要迁移】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_imppc/        # 警察学院 【需要迁移】
│   ├── java/
│   ├── logs/
│   └── vue/
└── hyrm_imusti/       # 监察官学院 【需要迁移】
    ├── java/
    ├── logs/
    └── vue/
```

**其他相关目录：/data/nfs_share/html/HOME_RUOYI_PROD/**
```
/data/nfs_share/html/HOME_RUOYI_PROD/
├── hyrm_demo/         # 演示站点 【不迁移-保留K8s】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_hy/           # 汇远主站 【已废弃】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_immu/         # 内蒙古医科大学 【已废弃】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_imnu/         # 内蒙古大学 【不迁移-保留K8s】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_regulate/     # 监管版本 【待确认】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_zyc/          # 中央财经大学 【待确认】
│   ├── java/
│   ├── logs/
│   └── vue/
├── hyrm_zyc_back/     # 中央财经大学备份 【待确认】
│   ├── java/
│   ├── logs/
│   ├── vue/
│   └── vue_back/
└── ruoyi_hyrm/        # 若依框架主站 【待确认】
    ├── java/
    ├── logs/
    └── vue/
```

#### 容器内部结构
- **应用目录**: `/workdir/` - 存放JAR文件和上传文件
- **前端目录**: `/home/<USER>/projects/ruoyi-ui/` - Vue前端静态文件
- **日志目录**: `/home/<USER>/logs/` - 应用日志

### 2.3 数据库环境
**当前数据库服务器**: 200.1.66.214
**目标数据库服务器**: 腾讯云 mysql-prod 容器

**相关数据库列表**:
- `hyrm` - 汇远融媒主服务 【需要迁移】
- `hyrm_news` - 消息模块数据库 【需要迁移】
- `hyrm_imppc` - 警察学院 【需要迁移】
- `hyrm_imusti` - 监察官学院 【需要迁移】
- `hyrm_imnu` - 内蒙古大学 【不迁移-保留214】
- `hyrm_demo` - 演示环境 【不迁移-保留214】
- `hyrm_regulate` - 监管版本 【待确认】
- `hyrm_zyc` - 中央财经大学 【待确认】
- 其他相关实例数据库...

## 3. 迁移策略

### 3.1 迁移原则
1. **分批迁移**: 先迁移演示环境，再迁移生产环境
2. **数据安全**: 迁移前完整备份所有数据
3. **服务连续性**: 采用蓝绿部署，确保服务不中断
4. **配置标准化**: 建立统一的Docker配置模板

### 3.2 目录架构设计
参考已建立的服务端应用管理规范：

```
/mnt/datadisk0/
├── apps/java-web/              # Java Web应用配置
│   ├── hyqm-prod/              # 汇远轻媒主服务
│   │   ├── conf/
│   │   │   ├── application.yml
│   │   │   └── nginx.conf
│   │   ├── logs/
│   │   ├── docker-compose.yml
│   │   ├── .env
│   │   └── manage.sh
│   ├── hyqm-qm/                # 汇远轻媒-无logo版本
│   ├── hyrm-imppc/             # 警察学院
│   └── hyrm-imusti/            # 监察官学院
└── volumns/java-web/           # 应用数据
    ├── hyqm-prod/
    │   ├── workdir/            # JAR文件和上传文件
    │   ├── ui/                 # 前端静态文件
    │   └── logs/               # 应用日志
    ├── hyqm-qm/
    ├── hyrm-imppc/
    └── hyrm-imusti/
```

### 3.3 容器配置设计

#### Docker Compose模板
```yaml
version: '3.8'

networks:
  1panel-network:
    external: true

services:
  hyqm-backend:
    image: 4525e4716f8b  # 使用现有镜像
    container_name: hyqm-${INSTANCE_NAME}-backend
    networks:
      - 1panel-network
    volumes:
      - /mnt/datadisk0/volumns/java-web/hyqm-${INSTANCE_NAME}/workdir:/workdir
      - /mnt/datadisk0/volumns/java-web/hyqm-${INSTANCE_NAME}/logs:/home/<USER>/logs
      - /mnt/datadisk0/apps/java-web/hyqm-${INSTANCE_NAME}/conf/application.yml:/workdir/application.yml
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql-prod
      - DB_PORT=3306
    restart: unless-stopped

  hyqm-frontend:
    image: nginx:alpine
    container_name: hyqm-${INSTANCE_NAME}-frontend
    networks:
      - 1panel-network
    volumes:
      - /mnt/datadisk0/volumns/java-web/hyqm-${INSTANCE_NAME}/ui:/usr/share/nginx/html
      - /mnt/datadisk0/apps/java-web/hyqm-${INSTANCE_NAME}/conf/nginx.conf:/etc/nginx/nginx.conf
    restart: unless-stopped
```

## 4. 迁移实施计划

### 4.1 准备阶段
1. **环境准备**
   - 在腾讯云服务器创建目录结构
   - 准备Docker镜像和配置模板
   - 配置网络和存储

2. **数据备份**
   - 备份NFS共享存储中的所有汇远融媒数据
   - 备份相关数据库
   - 记录当前配置参数

### 4.2 迁移阶段
1. **演示环境迁移** (hyrm-demo)
   - 数据传输和配置
   - 容器部署和测试
   - 功能验证

2. **生产环境迁移**
   - 按优先级迁移：hyqm-prod → hyqm-qm → hyrm-imppc → hyrm-imusti
   - 配置反向代理规则
   - 域名切换和数据库迁移

### 4.3 验证阶段
1. **功能测试**
   - 前端页面访问
   - 后端API接口
   - 数据库连接
   - 文件上传下载

2. **性能测试**
   - 响应时间对比
   - 并发处理能力
   - 资源使用情况

## 5. 风险评估与应对

### 5.1 主要风险
1. **数据丢失风险**: NFS到本地存储的数据传输
2. **服务中断风险**: 迁移过程中的服务停机
3. **配置错误风险**: 数据库连接和应用配置
4. **性能下降风险**: 新环境的性能表现

### 5.2 应对措施
1. **完整备份**: 迁移前进行全量数据备份
2. **蓝绿部署**: 保持原环境运行，新环境验证通过后切换
3. **配置验证**: 详细的配置检查清单
4. **回滚方案**: 快速回滚到原环境的应急预案

## 6. 时间安排

### 6.1 迁移时间表
- **第1天**: 环境准备和数据备份
- **第2天**: 演示环境迁移和测试
- **第3-4天**: 生产环境分批迁移
- **第5天**: 全面测试和优化

### 6.2 关键节点
- 数据备份完成检查点
- 演示环境验证通过检查点
- 生产环境切换检查点
- 最终验收检查点

## 7. 后续优化

### 7.1 监控告警
- 容器健康状态监控
- 应用性能监控
- 数据库连接监控

### 7.2 自动化运维
- 自动备份脚本
- 容器管理脚本
- 日志轮转配置

这个迁移规划将确保汇远融媒业务平稳从K8s环境迁移到Docker容器化环境，同时保持服务的稳定性和数据的完整性。
