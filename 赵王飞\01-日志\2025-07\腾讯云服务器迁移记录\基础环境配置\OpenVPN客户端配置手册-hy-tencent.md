# OpenVPN Access Server客户端配置手册 - hy-tencent

## 服务器信息
- 公网IP：***************
- 客户端名称：hy-tencent
- 服务器类型：腾讯云
- OpenVPN版本：OpenVPN Access Server (商业版)
- 容器名称：1Panel-openvpn-sNji

## 重要说明
本服务器使用的是OpenVPN Access Server（商业版），不是社区版。主要区别：
- 使用用户名密码认证，不是纯证书认证
- 有Web管理界面
- 支持更多高级功能
- 配置方式有所不同

## 1. 前置检查

### 1.1 确认Docker服务状态
```bash
# 检查Docker是否运行
sudo docker ps

# 如果需要启动Docker
sudo systemctl start docker
sudo systemctl enable docker
```

### 1.2 确认OpenVPN服务器容器状态
```bash
# 查看OpenVPN容器是否运行
sudo docker ps | grep openvpn

# 如果没有运行，启动OpenVPN服务器
sudo docker run -v /etc/openvpn:/etc/openvpn -d \
--name openvpn \
--restart always \
-p 1194:1194/udp \
--cap-add=NET_ADMIN \
kylemanna/openvpn
```

## 2. OpenVPN Access Server客户端配置生成

### 2.1 检查用户是否存在
```bash
# 检查用户状态
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user hy-tencent UserPropGet
```

### 2.2 设置服务器主机名（如需要）
```bash
# 设置公网IP为主机名
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --key "host.name" --value "***************" ConfigPut

# 重启服务使配置生效
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli start
```

### 2.3 设置用户密码
```bash
# 为用户设置密码（请替换为您想要的密码）
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user hy-tencent --new_pass YOUR_PASSWORD SetLocalPassword
```

### 2.4 生成客户端配置文件
```bash
# 生成客户端配置文件
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user hy-tencent GetUserlogin > /tmp/hy-tencent.ovpn

# 查看配置文件内容
cat /tmp/hy-tencent.ovpn
```

## 3. Windows客户端配置

### 3.1 准备工作
1. 下载并安装 OpenVPN GUI：https://openvpn.net/community-downloads/
2. 默认安装位置：`C:\Program Files\OpenVPN`

### 3.2 配置步骤
1. 获取配置文件：
   - 配置文件已生成：`hy-tencent.ovpn`
   - 可以通过scp下载或复制内容

2. 在Windows客户端：
   - 将 `hy-tencent.ovpn` 文件复制到 `C:\Program Files\OpenVPN\config` 目录

### 3.3 连接步骤
1. 运行 OpenVPN GUI（会在右下角显示图标）
2. 右键点击托盘图标
3. 选择 "Connect hy-tencent"
4. 输入认证信息：
   - 用户名：`hy-tencent`
   - 密码：`hy123456`（或您设置的密码）
5. 点击确定连接

### 3.4 连接验证
1. 连接成功后，托盘图标会变绿
2. 访问 https://ip.cn 查看IP是否变为 ***************
3. 使用命令验证：
   ```cmd
   ipconfig
   ping ***************
   ```

## 4. 故障排除

### 4.1 常见错误及解决方案

1. **"permission denied while trying to connect to the Docker daemon"**
   ```bash
   # 解决方案：使用sudo或将用户添加到docker组
   sudo usermod -aG docker $USER
   newgrp docker
   ```

2. **"All tap-windows6 adapters are in use"**
   - 关闭其他VPN连接
   - 在设备管理器中禁用并重新启用TAP适配器
   - 重启OpenVPN服务

3. **连接失败**
   - 检查服务器防火墙是否开放1194/UDP端口
   - 查看OpenVPN GUI日志（右键选择 "View Log"）
   - 确保配置文件格式正确

### 4.2 服务器端检查
```bash
# 检查OpenVPN服务状态
sudo docker ps | grep openvpn
sudo docker logs -f openvpn

# 检查端口是否开放
sudo netstat -tulnp | grep 1194

# 检查防火墙规则
sudo iptables -L -n | grep 1194
```

## 5. 安全建议

1. **配置文件安全**
   - 配置文件包含敏感信息，请妥善保管
   - 传输完成后建议从服务器删除
   - 使用安全方式传输到客户端

2. **证书管理**
   - 定期更换证书
   - 及时吊销不再使用的证书
   - 做好证书备份

3. **传输方式示例**
   ```bash
   # 从本地下载配置文件（在本地电脑执行）
   scp username@***************:/etc/openvpn/clients/hy-tencent.ovpn ./
   ```

## 6. 多客户端支持

如果需要为其他设备生成配置，重复步骤2.2-2.4，使用不同的客户端名称：

```bash
# 例如为手机生成配置
sudo docker run -v /etc/openvpn:/etc/openvpn --rm -it \
-e EASYRSA_BATCH=1 \
kylemanna/openvpn easyrsa build-client-full hy-tencent-mobile nopass

# 导出手机配置
sudo docker run -v /etc/openvpn:/etc/openvpn --rm kylemanna/openvpn ovpn_getclient hy-tencent-mobile | sudo tee /etc/openvpn/clients/hy-tencent-mobile.ovpn > /dev/null
```

## 7. 配置说明

- **服务器IP**: ***************
- **协议**: UDP
- **端口**: 1194
- **VPN子网**: ********/24（默认）
- **加密**: 无密码保护（便于使用）
- **自动重连**: 支持

完成以上步骤后，客户端就可以通过OpenVPN安全地连接到腾讯云服务器了。---
date_created: 2025-07-17 11:56:06
date_modified: 2025-07-17 11:56:06
author: 赵王飞
---
