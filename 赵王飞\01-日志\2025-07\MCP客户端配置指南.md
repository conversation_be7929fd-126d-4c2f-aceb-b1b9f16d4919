# MCP客户端配置指南

本文档总结了不同IDE和AI工具配置Graphiti MCP服务器的标准方法，基于官方文档和实际测试验证。

## 配置概述

Graphiti MCP服务器支持两种传输模式：
- **stdio模式**：适用于本地客户端，低延迟，直接进程通信
- **SSE模式**：适用于远程访问，基于HTTP，支持多客户端连接

## 各IDE配置方法

### 1. Trae AI

#### stdio模式（推荐）
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "uv",
      "args": [
        "run",
        "--isolated",
        "--directory",
        "D:\\Project\\node\\crawl_all\\django-vue3-admin\\graphiti\\mcp_server",
        "--project",
        ".",
        "graphiti_mcp_server.py",
        "--transport",
        "stdio"
      ],
      "env": {
        "NEO4J_URI": "bolt://***********:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "graphiti123",
        "OPENAI_API_KEY": "your_api_key_here",
        "OPENAI_BASE_URL": "https://api.siliconflow.cn/v1",
        "MODEL_NAME": "deepseek-chat"
      }
    }
  }
}
```

#### SSE模式
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "sse",
      "url": "http://localhost:8000/sse"
    }
  }
}
```

### 2. Claude Desktop

#### stdio模式（推荐）
配置文件位置：`~/.claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "uv",
      "args": [
        "run",
        "--isolated",
        "--directory",
        "D:\\Project\\node\\crawl_all\\django-vue3-admin\\graphiti\\mcp_server",
        "--project",
        ".",
        "graphiti_mcp_server.py",
        "--transport",
        "stdio"
      ],
      "env": {
        "NEO4J_URI": "bolt://***********:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "graphiti123",
        "OPENAI_API_KEY": "your_api_key_here",
        "OPENAI_BASE_URL": "https://api.siliconflow.cn/v1",
        "MODEL_NAME": "deepseek-chat"
      }
    }
  }
}
```

#### SSE模式（需要网关）
⚠️ **注意**：Claude Desktop不原生支持SSE，需要使用mcp-remote网关

```json
{
  "mcpServers": {
    "graphiti-memory": {
      "command": "npx",
      "args": [
        "mcp-remote",
        "http://localhost:8000/sse"
      ]
    }
  }
}
```

### 3. Cursor

#### SSE模式（推荐）
配置文件位置：`~/.cursor/mcp.json`

```json
{
  "mcpServers": {
    "graphiti-memory": {
      "url": "http://localhost:8000/sse"
    }
  }
}
```

#### stdio模式
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "uv",
      "args": [
        "run",
        "--isolated",
        "--directory",
        "D:\\Project\\node\\crawl_all\\django-vue3-admin\\graphiti\\mcp_server",
        "--project",
        ".",
        "graphiti_mcp_server.py",
        "--transport",
        "stdio"
      ],
      "env": {
        "NEO4J_URI": "bolt://***********:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "graphiti123",
        "OPENAI_API_KEY": "your_api_key_here",
        "OPENAI_BASE_URL": "https://api.siliconflow.cn/v1",
        "MODEL_NAME": "deepseek-chat"
      }
    }
  }
}
```

### 4. Windsurf

#### SSE模式
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "serverUrl": "http://localhost:8000/sse"
    }
  }
}
```

#### stdio模式
```json
{
  "mcpServers": {
    "graphiti-memory": {
      "transport": "stdio",
      "command": "uv",
      "args": [
        "run",
        "--isolated",
        "--directory",
        "D:\\Project\\node\\crawl_all\\django-vue3-admin\\graphiti\\mcp_server",
        "--project",
        ".",
        "graphiti_mcp_server.py",
        "--transport",
        "stdio"
      ],
      "env": {
        "NEO4J_URI": "bolt://***********:7687",
        "NEO4J_USER": "neo4j",
        "NEO4J_PASSWORD": "graphiti123",
        "OPENAI_API_KEY": "your_api_key_here",
        "OPENAI_BASE_URL": "https://api.siliconflow.cn/v1",
        "MODEL_NAME": "deepseek-chat"
      }
    }
  }
}
```

## 配置差异总结

| IDE | stdio模式 | SSE模式字段 | 特殊说明 |
|-----|-----------|-------------|----------|
| Trae AI | ✅ 标准格式 | `transport` + `url` | 完全支持两种模式 |
| Claude Desktop | ✅ 标准格式 | 需要`mcp-remote`网关 | 不原生支持SSE |
| Cursor | ✅ 标准格式 | 仅需`url`字段 | 原生支持SSE |
| Windsurf | ✅ 标准格式 | 使用`serverUrl`字段 | 字段名称略有不同 |

## 启动服务器

### SSE模式服务器启动
在使用SSE模式前，需要先启动Graphiti MCP服务器：

```bash
cd D:\Project\node\crawl_all\django-vue3-admin\graphiti\mcp_server
python graphiti_mcp_server.py --transport sse
```

服务器将在 `http://localhost:8000/sse` 上运行。

### stdio模式
stdio模式不需要预先启动服务器，客户端会自动启动进程。

## 环境变量说明

所有stdio模式配置都需要以下环境变量：

```bash
# Neo4j数据库配置
NEO4J_URI=bolt://***********:7687
NEO4J_USER=neo4j
NEO4J_PASSWORD=graphiti123

# AI服务配置
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=https://api.siliconflow.cn/v1
MODEL_NAME=deepseek-chat

# 可选配置
SMALL_MODEL_NAME=deepseek-chat
LLM_TEMPERATURE=0.7
SEMAPHORE_LIMIT=5
```

## 推荐配置

- **本地开发**：推荐使用stdio模式，性能更好，延迟更低
- **远程访问**：使用SSE模式，支持多客户端连接
- **Claude Desktop**：推荐stdio模式，避免mcp-remote网关的复杂性
- **Cursor/Windsurf**：两种模式都可以，根据使用场景选择

## 故障排除

1. **stdio模式启动失败**：检查uv是否安装，路径是否正确
2. **SSE模式连接失败**：确认服务器已启动，端口8000未被占用
3. **环境变量问题**：确认所有必需的环境变量已正确设置
4. **Neo4j连接失败**：检查数据库服务状态和网络连接

## 参考文档

- [Graphiti MCP服务器官方文档](https://github.com/getzep/graphiti/blob/main/mcp_server/README.md)
- [MCP协议规范](https://modelcontextprotocol.io/)
- [各IDE MCP配置指南](https://playbooks.com/mcp/zep-graphiti)