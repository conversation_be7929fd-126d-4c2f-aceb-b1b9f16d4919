
# 背景
我们需要将旧的208服务器的web服务迁移到新的腾讯云上,相关情况, 先完成数据迁移, 然后再进行腾讯云上的重建.
1. /usr/share/nginx/html可能会非常大，甚至有很多日志文件，我们先需要了解这些情况


### 旧服务器环境信息查看

``` bash
root@hy08:/usr/share/nginx/html# php -v
PHP Warning:  PHP Startup: Unable to load dynamic library 'pdo_pgsql' (tried: /usr/lib/php/20170718/pdo_pgsql (/usr/lib/php/20170718/pdo_pgsql: cannot open shared object file: No such file or directory), /usr/lib/php/20170718/pdo_pgsql.so (/usr/lib/php/20170718/pdo_pgsql.so: undefined symbol: pdo_parse_params)) in Unknown on line 0
PHP Warning:  Module 'pgsql' already loaded in Unknown on line 0
PHP 7.2.24-0ubuntu0.18.04.7 (cli) (built: Oct  7 2020 15:24:25) ( NTS )
Copyright (c) 1997-2018 The PHP Group
Zend Engine v3.2.0, Copyright (c) 1998-2018 Zend Technologies
    with Zend OPcache v7.2.24-0ubuntu0.18.04.7, Copyright (c) 1999-2018, by Zend Technologies

root@hy08:/usr/share/nginx/html# apache2 -v
Server version: Apache/2.4.29 (Ubuntu)
Server built:   2020-08-12T21:33:25
```

### 业务目录
``` bash
cd /usr/share/nginx/html
ls
ad_delivery          CommonService     favicon.ico        gaokao_admin          huiyuan_crm   icbc-app            js             pearproject        picc        shop-wenlv          watermelon   zsgj        zsqc-admin     zsqc-hsjc     zsqc-static-admin  zsqc-ysf-back
business-card-admin  community-admin   game_break_bricks  gaokao_shop           hyqm2.0       index.html          message_admin  pearproject_admin  qcgj        smart_waiting_room  yinji        zslh        zsqc-app       zsqc-icbc     zsqc-subway        zsqc-ysf-back0907
client_ip.php        doc               game_fruit         get-weixin-code.html  hyqm2-tenant  index.php           nmgicbc-admin  pearproject_api    robots.txt  toolbox             yinji-admin  zslh-admin  zsqc-epidemic  zsqc-mapline  zsqc-usercenter
cloud_enjoy_window   doc-manage-admin  gaokao             help                  hyqm2-test    jiaodui-django-vue  obsidian       php-monitor        school_bus  toolbox_php         yxsc_cli     zsqc        zsqc-garbage   zsqc_shop     zsqc-ysf


```

### 空间占用分析

对 `/usr/share/nginx/html` 目录（总大小51GB）的深入分析发现，绝大部分空间被少数几个项目占用。具体情况如下表所示：

| 项目目录             | 总大小 | 主要占用内容                               | 大小   | 内容类型     | 初步建议                                     |
| -------------------- | ------ | ------------------------------------------ | ------ | ------------ | -------------------------------------------- |
| `yinji-admin`        | 15 GB  | `public/wechat/m4a`                        | 9.7 GB | 音频文件     | 与业务方确认是否可清理或只迁移近期数据       |
| `cloud_enjoy_window` | 12 GB  | `public/uploads/files`                     | 11 GB  | 用户上传文件 | 与业务方确认是否可清理或只迁移近期数据       |
| `zsqc-admin`         | 7.2 GB | `runtime/log`                              | 6.4 GB | **日志文件** | **可安全清理，迁移前删除**                   |
| `zsgj`               | 5.8 GB | `Public/portrait`                          | 3.4 GB | 用户头像     | 确认是否需要全量迁移                         |
|                      |        | `Index/Runtime/Logs`                       | 1.7 GB | **日志文件** | **可安全清理，迁移前删除**                   |
|                      |        | `Public/apk`                               | 853 MB | APP安装包    | 确认是否为历史版本，是否可清理               |
| `yinji`              | 4.7 GB | `.git`                                     | 2.2 GB | **Git版本库**  | **必须排除，仅部署最新代码**                 |
|                      |        | `public/static` (media, audio)             | 2.2 GB | 媒体文件     | 确认是否需要全量迁移                         |

**总结**：

1.  **可清理空间**：`zsqc-admin` 和 `zsgj` 中的日志文件共计 **8.1GB**，可以安全删除。
2.  **必须排除**：`yinji` 项目中 **2.2GB** 的 `.git` 目录必须在打包时排除。
3.  **待决策数据**：剩余约 **30GB+** 的数据主要是用户上传的各类文件和媒体资源。这些需要与业务方进一步确认，判断是否可以只迁移部分或进行归档处理。

### 架构分析

经过对服务器进程、端口和配置文件的深入排查，我们得出了关于服务器真实角色的关键结论：

1.  **核心角色：Nginx 反向代理网关**
    -   服务器的**主要功能**是作为一台网关，接收所有外部请求，然后根据 Nginx 的配置，将请求转发到多台不同的后端服务器（如 `200.1.66.209`, `200.1.66.210`）或 `Rancher` 容器平台。
    -   所有外部 HTTP (80) 流量均由 Nginx 处理。**它本身并不是一台应用服务器**。

2.  **历史遗留产物**
    -   服务器上运行的 **Apache 服务**（监听 8080 端口）和完整的 **PHP 环境**是**历史遗留配置，当前并未在生产环境中使用**。
    -   `/usr/share/nginx/html` 目录下的 **51GB 文件**，其性质更像是**代码仓库、文件备份或开发目录**，而不是由这台服务器直接提供服务的生产代码。

**迁移任务核心变更**：

-   迁移的重点**不再是迁移PHP应用**，而是**迁移并重建 Nginx 网关配置**。
-   本地的 Apache 和 PHP 环境**无需迁移**。
-   对于 51GB 的文件，需要决策如何处理：**作为归档迁移**，还是**直接废弃**。

### 迁移策略调整（2025-07-23）

**背景**：
在尝试压缩最大的项目 `yinji-admin` (15GB) 时，由于该项目的部分目录（`public/wechat/m4a`）正在被线上服务持续写入文件，导致 `tar` 命令因 `file changed as we read it` 错误而失败。这表明对正在提供服务的大型目录进行热备份存在风险和不确定性。

**新策略**：
根据决策，迁移策略调整为更灵活、更高效的**分批次、服务器直传**模式：

1.  **分批迁移**：不再一次性压缩所有文件，而是从选定的小项目（如 `pearproject`）开始，逐个迁移。
2.  **创建中转目录**：在源服务器（208）上创建临时中转目录 `/root/web_backups_20250723/transfer/`，用于存放待传输的压缩包。
3.  **服务器直传**：在源服务器上针对该目录启动一个临时 HTTP 服务，然后在腾讯云目标服务器上使用 `wget` 直接下载压缩包，利用云内网的高速传输，避免本地中转。
4.  **目标路径**：文件将被直接下载并解压到腾讯云服务器的 `/mnt/datadisk0/volumns/php_home_208` 目录中。

此方案将大大提高迁移效率，并允许对每个项目的迁移过程进行独立控制。


---
### 2025-07-24：服务部署与问题排查

#### 1. 部署 `apache_php_service` 容器
- **实施内容**：执行 `docker run` 命令，基于 `zhao0829wang/apache2-php:7.2-rc4` 镜像启动了 `apache_php_service` 容器。
- **关键参数配置**：
  - 容器名称：`apache_php_service`
  - 端口映射：`8088:80`
  - 目录挂载：
    - `/mnt/datadisk0/volumns/php_home_208:/var/www/html`
    - `/mnt/datadisk0/volumns/php_home_208/default.conf:/etc/apache2/sites-enabled/default.conf`

#### 2. 创建业务数据库与用户
- **实施内容**：在 `mysql-prod` 容器中创建了项目所需的数据库和用户。
- **关键参数配置**：
  - **数据库**：`pearproject`, `pearproject_admin`
  - **用户**：`pearproject`
  - **密码**：`kG7#tPq9@zR2$vX1` (已生成并提供)

#### 3. 排查并解决网络连接问题
- **问题现象**：PHP 应用报错 `php_network_getaddresses: getaddrinfo failed: Name or service not known`。
- **选择原因**：经排查，原因是 `apache_php_service` 容器被默认连接到了 `bridge` 网络，无法通过容器名称 `mysql-prod` 解析到数据库。为了保证配置的持久性和规范性，我们选择了“重新创建容器”而非“动态连接网络”的方案。
- **实施内容**：
  1. 停止并删除了旧的 `apache_php_service` 容器。
  2. 使用 `docker run` 命令重新创建了容器，并增加了 `--network=1panel-network` 参数，将其正确连接到 `mysql-prod` 所在的用户自定义网络。

#### 4. 排查并解决文件权限问题
- **问题现象**：PHP 应用报错 `session_start(): ... Permission denied (13)`。
- **选择原因**：经排查，原因是 Docker 挂载的宿主机目录 `runtime` 的所有权与容器内运行 Apache 的 `www-data` 用户不匹配，导致 PHP 进程没有写入权限。
- **实施内容**：
  - 在宿主机上执行 `sudo chown -R www-data:www-data /mnt/datadisk0/volumns/php_home_208/pearproject_api/runtime` 命令，递归地将 `runtime` 目录的所有权赋予 `www-data` 用户，从根本上解决了权限问题。

---
### 2025-07-24：架构重构与安全优化

#### 1. 发现安全问题
- **问题现象**：`apache_php_service` 容器直接暴露 8088 端口到公网，配置文件和压缩包暴露在 web 根目录。
- **安全风险**：
  - 任何人可通过 `公网IP:8088` 直接访问
  - 敏感配置文件可被下载
  - 不符合容器化安全最佳实践

#### 2. 实施架构重构
- **设计理念**：采用数据与配置分离的架构，参考 1Panel 管理模式
- **目录重构**：
  ```
  /mnt/datadisk0/
  ├── apps/php_home_208/           # 应用配置目录
  │   ├── conf/apache2/            # Apache配置
  │   ├── logs/                    # 日志文件
  │   ├── docker-compose.yml       # 容器编排
  │   ├── .env                     # 环境变量
  │   ├── manage.sh               # 管理脚本
  │   └── README.md               # 应用文档
  └── volumns/php_home_208/        # 应用数据目录
      ├── pearproject/             # 项目数据
      ├── pearproject_admin/
      └── pearproject_api/
  ```

#### 3. 安全优化措施
- **移除端口映射**：容器不再直接暴露端口到公网
- **配置文件隔离**：所有配置文件移至 `/mnt/datadisk0/apps/php_home_208/conf/`
- **网络隔离**：使用 `1panel-network` 内部网络
- **临时文件清理**：移除 web 根目录中的压缩包和配置文件

#### 4. 管理工具完善
- **管理脚本**：创建 `manage.sh` 支持 start/stop/restart/status/logs/shell 等操作
- **标准化配置**：建立统一的 docker-compose.yml 和环境变量管理
- **文档完善**：创建应用级 README 和架构设计文档

#### 5. 验证结果
- ✅ 容器正常运行，无端口暴露
- ✅ 应用通过内部网络正常访问
- ✅ 配置文件安全隔离
- ✅ 日志独立存储和管理
- ✅ 符合容器化安全最佳实践

#### 6. 反向代理配置与网络问题解决
- **网络问题发现**：OpenResty在host网络模式，无法通过容器名访问1panel-network中的容器
- **解决方案**：使用IP地址 `***********` 配置反向代理
- **配置验证**：在1Panel中成功配置反向代理到 `http://***********`

#### 7. Apache安全配置迁移
- **迁移需求**：将原Nginx复杂的安全配置迁移到Apache层面
- **实施策略**：使用.htaccess文件实现目录级安全控制
- **安全措施实现**：
  - ✅ 全局.git目录保护：`RedirectMatch 403 /\.git`
  - ✅ 隐藏文件保护：禁止访问以.开头的文件
  - ✅ 敏感文件保护：禁止访问.env, .ini, .log等文件
  - ✅ 目录访问控制：pearproject_admin根目录403，public目录允许访问
- **配置文件位置**：
  - 主配置：`/mnt/datadisk0/apps/php_home_208/conf/apache2/default.conf`
  - 全局安全：`/mnt/datadisk0/volumns/php_home_208/.htaccess`
  - 目录级安全：各目录下的`.htaccess`文件
- **测试结果**：
  - `pearproject_admin/` → 403 Forbidden ✅
  - `pearproject_admin/public/` → 200 OK ✅
  - `pearproject_admin/.git/` → 403 Forbidden ✅

#### 8. 当前状态总结
- ✅ 容器安全运行，无端口暴露
- ✅ 反向代理正常工作
- ✅ 安全配置有效防护
- ✅ 架构符合容器化最佳实践
- ✅ 为后续应用迁移建立了标准模板

#### 9. 后续规划
- 迁移汇远融煤业务
- 完善其他目录的安全配置
- 建立统一的服务端应用管理规范
