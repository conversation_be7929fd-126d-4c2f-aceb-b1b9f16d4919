# 
# 背景

目前使用 1panel 创建 Docker 数据库时，无法选择数据目录，导致数据库默认创建到了系统盘。而实际上，我们需要将数据保存到容量更大的数据盘上，以确保系统盘空间充足和数据安全。

## 诊断与分析

通过一系列诊断命令，我们确认了问题的现状。

### 1. 定位 MySQL 容器

首先，找到服务器上运行的两个 MySQL 容器。

**命令**:
```bash
docker ps -a | grep mysql
```
**输出**:
```
96148916d2ba   mysql:5.7.44   ...   mysql-test
eab1b53fbdbd   mysql:5.7.44   ...   mysql-prod
```

### 2. 确认数据存储位置

通过 `docker inspect` 查看容器挂载信息，确定数据在主机上的物理路径。

**命令**:
```bash
docker inspect mysql-test
```
**关键输出**:
```json
"Binds": [
    "/opt/1panel/apps/mysql/mysql-test/data:/var/lib/mysql:rw",
    ...
]
```
这表明 `mysql-test` 的数据存储在主机的 `/opt/1panel/apps/mysql/mysql-test/data` 目录。

### 3. 查看磁盘分区情况

使用 `df -h` 命令检查磁盘挂载情况，判断 `/opt` 目录所属的磁盘。

**命令**:
```bash
df -h
```
**关键输出**:
```
Filesystem      Size  Used Avail Use% Mounted on
/dev/vda2        50G   13G   35G  26% /
/dev/vdb        492G   13G  454G   3% /mnt/datadisk0
```

**结论**: `/opt` 目录位于根分区 `/` 下，属于系统盘 (`/dev/vda2`)。因此，MySQL 数据确实存储在系统盘上。

## 解决方案

采纳用户提出的精确、低风险方案：仅迁移 MySQL 的 `data` 目录到数据盘，并使用符号链接（Symbolic Link）将其链接回原位。这样既解决了空间问题，又最大限度地避免了对 1panel 核心配置的干扰。

## 实施步骤

### 1. 停止 MySQL 容器
```bash
docker stop mysql-test mysql-prod
```

### 2. 迁移数据目录
将两个容器的数据目录移动到数据盘 `/mnt/datadisk0/` 下的新建目录中。注意：由于文件权限问题，必须使用 `sudo`。
```bash
sudo mkdir -p /mnt/datadisk0/mysql_data
sudo mv /opt/1panel/apps/mysql/mysql-test/data /mnt/datadisk0/mysql_data/mysql-test_data
sudo mv /opt/1panel/apps/mysql/mysql-prod/data /mnt/datadisk0/mysql_data/mysql-prod_data
```

### 3. 创建符号链接
在原路径创建指向新数据目录的符号链接。
```bash
sudo ln -s /mnt/datadisk0/mysql_data/mysql-test_data /opt/1panel/apps/mysql/mysql-test/data
sudo ln -s /mnt/datadisk0/mysql_data/mysql-prod_data /opt/1panel/apps/mysql/mysql-prod/data
```

### 4. 重启并验证服务
重启容器并检查其运行状态，确认服务已通过符号链接正常加载数据盘上的数据。
```bash
docker start mysql-test mysql-prod
docker ps | grep mysql
```
**输出**:
```
96148916d2ba   mysql:5.7.44   ...   Up 12 seconds   ...   mysql-test
eab1b53fbdbd   mysql:5.7.44   ...   Up 8 seconds    ...   mysql-prod
```

### 五、问题深化与解决

在执行完上述步骤后，发现 `mysql-test` 容器依然处于不断重启的状态，而 `mysql-prod` 容器运行正常。这表明问题并非全局配置错误，而是 `mysql-test` 的局部问题。经过一系列深入排查，过程如下：

1.  **初步诊断：**
    -   **现象**：`mysql-test` 容器日志反复出现 `mysqld: Table 'mysql.plugin' doesn't exist` 错误。
    -   **排查**：检查了数据目录的所有权和权限，确认为正确的 `999:1001`，排除了权限问题。

2.  **深入排查：挂载冲突**
    -   **发现问题**：通过 `mount | grep mysql` 命令，发现一个未预期的关键问题：
        ```bash
        /dev/vdb on /opt/1panel/apps/mysql/mysql-test/data type ext4 (rw,relatime)
        /dev/vdb on /opt/1panel/apps/mysql/mysql-prod/data type ext4 (rw,relatime)
        ```
    -   **问题分析**：两个容器的数据目录被同时挂载到了同一个物理设备 `/dev/vdb` 上，这是一个严重的文件系统冲突。`prod` 容器可能因先启动而抢占了设备，导致 `test` 容器在读取被“污染”的数据时持续失败。

3.  **解决方案验证（在 `prod` 上）**
    -   **目标**：验证清理错误挂载后，符号链接方案是否可行。
    -   **步骤**：
        1.  停止 `mysql-prod` 容器。
        2.  强制卸载错误挂载：`sudo umount -l /opt/1panel/apps/mysql/mysql-prod/data`
        3.  重建符号链接：`sudo ln -s /mnt/datadisk0/volumns/mysql/mysql-prod_data /opt/1panel/apps/mysql/mysql-prod/data`
        4.  重启 `mysql-prod` 容器。
    -   **结果**：`mysql-prod` 容器完美正常运行。**这证明了符号链接方案在清理掉底层干扰后是完全正确的。**

4.  **最终诊断：`test` 容器数据损坏**
    -   **复现操作**：在 `mysql-test` 容器上执行了与 `prod` 完全相同的清理和修复操作。
    -   **现象**：`test` 容器依然无限重启，但日志中已无明显错误。
    -   **结论**：`test` 容器的数据目录，在之前与 `prod` 容器的设备挂载冲突中，其内部文件已被损坏，导致 MySQL 服务无法加载，直接崩溃。

### 六、最终修复：重建 `test` 容器数据

基于以上诊断，最终解决方案是为 `test` 容器重建一个全新的、干净的数据目录，让其重新初始化。

1.  停止 `mysql-test` 容器。
2.  归档已损坏的数据目录：`sudo mv /mnt/datadisk0/volumns/mysql/mysql-test_data /mnt/datadisk0/volumns/mysql/mysql-test_data_backup`
3.  创建全新的空数据目录：`sudo mkdir /mnt/datadisk0/volumns/mysql/mysql-test_data`



你可以直接使用ssh MCP获取服务器的信息。
注意：对于类似删除这样的敏感操作，必须经过用户审批之后才能执行。行，任何命令之前,请自行评估风险。
