# Docker容器网络最佳实践指南

## 概述

本文档总结了Docker容器间网络通信的最佳实践，包括网络架构设计、调试方法和常见问题解决方案。适用于生产环境和开发环境的容器网络管理。

## 核心原则

### 1. 网络隔离与共享平衡
- **业务隔离**：不同业务使用独立网络
- **资源共享**：中间件服务连接多个网络
- **安全优先**：最小化端口暴露

### 2. 容器命名通信
- 使用容器名称而非IP地址
- 便于维护和扩展
- 自动DNS解析

## 网络架构设计

### 单业务架构
```bash
# 创建业务专用网络
docker network create --driver bridge app-network

# 启动服务容器
docker run -d --name web-app --network app-network nginx
docker run -d --name database --network app-network mysql:5.7

# 应用配置
# database_host: database
# database_port: 3306
```

### 多业务共享架构（推荐）
```
业务A网络 (app-a-network)     业务B网络 (app-b-network)
    |                           |
app-a-web ←→ app-a-api         app-b-web ←→ app-b-api
    |                           |
    └─────────→ 数据库 ←─────────┘
              (连接多个网络)
```

**实施步骤：**
```bash
# 1. 创建网络
docker network create --driver bridge shared-services
docker network create --driver bridge business-a
docker network create --driver bridge business-b

# 2. 启动共享服务
docker run -d --name mysql-prod --network shared-services mysql:5.7
docker run -d --name redis-prod --network shared-services redis:6

# 3. 连接共享服务到业务网络
docker network connect business-a mysql-prod
docker network connect business-a redis-prod
docker network connect business-b mysql-prod
docker network connect business-b redis-prod

# 4. 启动业务容器
docker run -d --name app-a-web --network business-a nginx
docker run -d --name app-b-web --network business-b nginx
```

## 容器网络连接管理

### 基本操作
```bash
# 查看网络列表
docker network ls

# 创建自定义网络
docker network create --driver bridge my-network

# 将容器连接到网络
docker network connect my-network container-name

# 断开网络连接
docker network disconnect bridge container-name

# 查看网络详情
docker network inspect my-network
```

### 容器多网络连接
一个容器可以同时连接多个网络：
```bash
# 容器同时连接到多个网络
docker network connect network-1 my-container
docker network connect network-2 my-container

# 验证连接
docker inspect my-container | grep -A 15 "Networks"
```

## 端口映射策略

### 内部通信 vs 外部访问

**内部通信（容器间）：**
- 不需要 `-p` 端口映射
- 直接使用容器名:端口
- 示例：`mysql-prod:3306`

**外部访问（主机/外网）：**
- 需要 `-p` 端口映射
- 安全绑定示例：
  ```bash
  # 仅本地访问
  -p 127.0.0.1:3306:3306
  
  # 外部访问
  -p 0.0.0.0:8080:80
  
  # 指定IP访问
  -p ***********:3306:3306
  ```

### 安全配置建议
```bash
# 数据库 - 仅内部访问
docker run -d --name mysql-prod \
  --network app-network \
  mysql:5.7

# Web服务 - 外部访问
docker run -d --name web-app \
  --network app-network \
  -p 80:80 \
  nginx
```

## 网络调试最佳实践

### 方法一：使用现有工具
```bash
# telnet测试端口
docker exec -it container-name telnet target-host 3306

# netcat测试连接
docker exec -it container-name nc -zv target-host 3306

# curl测试HTTP
docker exec -it container-name curl -I http://target-host:80
```

### 方法二：专用调试容器（推荐）
```bash
# 使用netshoot调试容器
docker run -it --rm --network target-network nicolaka/netshoot

# 在调试容器中执行
ping mysql-prod
nslookup mysql-prod
telnet mysql-prod 3306
curl http://web-service:80
```

**netshoot容器优势：**
- 包含完整网络工具集
- 用完即删，不污染环境
- 支持所有网络调试需求

### 方法三：临时安装工具
```bash
# Alpine容器
docker exec -it container-name sh
apk add --no-cache iputils curl

# Ubuntu/Debian容器
docker exec -it container-name bash
apt-get update && apt-get install -y iputils-ping curl

# CentOS/RHEL容器
docker exec -it container-name bash
yum install -y iputils curl
```

## 常见问题诊断

### 问题1：容器间无法通信
**症状：** 应用无法连接数据库
**诊断：**
```bash
# 检查容器网络归属
docker inspect app-container | grep -A 10 "Networks"
docker inspect db-container | grep -A 10 "Networks"

# 检查网络连通性
docker run -it --rm --network app-network nicolaka/netshoot ping db-container
```

**解决方案：**
```bash
# 将容器连接到同一网络
docker network connect shared-network app-container
docker network connect shared-network db-container
```

### 问题2：端口访问失败
**症状：** 外部无法访问服务
**诊断：**
```bash
# 检查端口映射
docker ps | grep container-name

# 检查端口绑定
netstat -tlnp | grep :8080
```

**解决方案：**
```bash
# 正确的端口映射
docker run -d -p 0.0.0.0:8080:80 nginx
```

### 问题3：DNS解析失败
**症状：** 容器名无法解析
**诊断：**
```bash
# 在调试容器中测试DNS
docker run -it --rm --network target-network nicolaka/netshoot nslookup target-container
```

**解决方案：**
- 确保容器在同一网络中
- 检查容器名称拼写
- 重启容器刷新DNS

## 网络验证命令集

### 基础检查
```bash
# 查看所有网络
docker network ls

# 查看容器网络配置
docker inspect container-name | grep -A 15 "Networks"

# 查看网络中的容器
docker network inspect network-name
```

### 连通性测试
```bash
# 使用专用调试容器
docker run -it --rm --network target-network nicolaka/netshoot

# 在调试容器中执行
ping target-container
telnet target-container 3306
curl http://target-container:8080/health
nslookup target-container
```

### 应用层测试
```bash
# 数据库连接测试
docker exec -it app-container mysql -h mysql-prod -u root -p

# HTTP服务测试
docker exec -it app-container curl http://api-service:8080/status
```

## 生产环境建议

### 网络命名规范
```bash
# 按业务划分
business-a-network
business-b-network
shared-services-network

# 按环境划分
prod-app-network
staging-app-network
dev-app-network
```

### 安全配置
- 数据库等敏感服务不暴露外部端口
- 使用防火墙规则限制网络访问
- 定期审查网络配置和连接

### 监控和维护
```bash
# 定期清理未使用的网络
docker network prune

# 监控网络连接状态
docker network inspect network-name

# 备份网络配置
docker network ls > networks-backup.txt
```

## 快速参考

### 常用命令速查
```bash
# 网络管理
docker network create my-network
docker network connect my-network container-name
docker network disconnect my-network container-name
docker network inspect my-network

# 调试命令
docker run -it --rm --network target-network nicolaka/netshoot
docker exec -it container-name telnet target 3306

# 信息查看
docker ps
docker inspect container-name | grep -A 15 "Networks"
```

### 配置模板
```yaml
# docker-compose.yml 示例
version: '3.8'
services:
  web:
    image: nginx
    networks:
      - frontend
    ports:
      - "80:80"
  
  api:
    image: my-api
    networks:
      - frontend
      - backend
  
  database:
    image: mysql:5.7
    networks:
      - backend
    # 不暴露外部端口

networks:
  frontend:
    driver: bridge
  backend:
    driver: bridge
```

## 总结

Docker容器网络的核心是合理的网络架构设计和有效的调试方法。通过遵循本文档的最佳实践，可以构建安全、高效、易维护的容器网络环境。

**关键要点：**
1. 使用自定义网络而非默认bridge
2. 容器名称通信优于IP地址
3. 合理设计网络隔离和共享
4. 使用专用调试容器进行网络诊断
5. 最小化端口暴露，优先内部通信
