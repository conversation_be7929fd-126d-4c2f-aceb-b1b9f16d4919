# hyqm_prod (汇远轻媒主服务) 迁移计划

## 1. 项目基本信息

### 1.1 项目概述
- **项目名称**：hyqm_prod (汇远轻媒主服务)
- **项目类型**：Java Web应用 (Spring Boot + Vue)
- **当前环境**：K8s集群 + NFS共享存储
- **目标环境**：Docker容器 + 本地卷挂载

### 1.2 技术架构
```
前端：Vue.js (Nginx静态文件服务)
后端：Spring Boot (Java应用)
数据库：MySQL (主库 + 消息库)
缓存：Redis
消息队列：RabbitMQ
```

## 2. 迁移范围分析

### 2.1 数据库依赖
根据汇远融媒架构分析，hyqm_prod需要以下数据库：
- **hyrm** - 汇远融媒主服务数据库 (主要业务数据) **26.3GB** ⚠️
- **hyrm_news** - 消息模块数据库 (已迁移完成 ✅)

### 2.2 文件存储分析
**NFS存储路径**：`/data/nfs_share/html/HOME_PROD/hyqm_prod/` **267MB**
```
hyqm_prod/
├── java/          # 后端JAR文件和配置
├── logs/          # 应用日志文件
└── vue/           # 前端静态文件
```

### 2.3 存储空间评估
- **源数据库大小**：26.3GB (hyrm主库)
- **应用文件大小**：267MB
- **目标服务器可用空间**：427GB ✅
- **预计传输时间**：数据库约60-90分钟，文件约5分钟

### 2.3 容器镜像信息
根据K8s环境分析，预计使用镜像：`4525e4716f8b` (汇远融媒标准镜像)

## 3. 迁移前准备工作

### 3.1 环境检查
- [ ] 确认208服务器mysqldump工具可用
- [ ] 确认腾讯云Docker环境正常运行
- [ ] 确认mysql-prod容器状态正常
- [ ] 确认1panel-network网络连通性
- [ ] 确认文件传输域名可访问

### 3.2 数据收集
- [ ] 确认hyrm数据库大小和表结构
- [ ] 确认hyqm_prod文件存储大小
- [ ] 确认当前K8s容器配置参数
- [ ] 确认端口和域名配置需求
- [ ] 确认依赖的外部服务

### 3.3 风险评估
- **数据风险**：hyrm主数据库26.3GB，传输时间60-90分钟 ⚠️
- **服务风险**：主服务迁移可能影响用户访问
- **配置风险**：数据库连接配置需要更新
- **依赖风险**：确保Redis、RabbitMQ等服务可用
- **存储风险**：大文件传输过程中的网络稳定性

## 4. 详细迁移计划

### 4.1 阶段一：数据库迁移 (预计90分钟) ⚠️

#### 步骤1：导出hyrm主数据库
```bash
# 在208服务器执行
mysqldump -h 200.1.66.214 -u root -p'TxkjDB2020#' \
  --single-transaction --routines --triggers hyrm > /tmp/hyrm.sql

# 检查文件大小
ls -lh /tmp/hyrm.sql
```

#### 步骤2：压缩并传输
```bash
# 压缩文件
tar -czf /usr/share/nginx/html/transfer/hyrm.sql.tar.gz /tmp/hyrm.sql

# 腾讯云下载
wget -O /tmp/hyrm.sql.tar.gz https://main.51zsqc.com/transfer/hyrm.sql.tar.gz
```

#### 步骤3：数据库导入
```bash
# 解压并复制到容器
cd /tmp && tar -xzf hyrm.sql.tar.gz
docker cp /tmp/hyrm.sql mysql-prod:/tmp/

# 创建数据库
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "CREATE DATABASE IF NOT EXISTS hyrm DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 处理GTID并导入
docker exec mysql-prod bash -c "sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' /tmp/hyrm.sql > /tmp/hyrm_clean.sql"
docker exec mysql-prod bash -c "mysql -u root -p'56d9DavJ*zwrwj9rmA' hyrm < /tmp/hyrm_clean.sql"

# 配置权限
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "GRANT ALL PRIVILEGES ON hyrm.* TO 'pearproject'@'%'; FLUSH PRIVILEGES;"
```

### 4.2 阶段二：应用文件迁移 (预计20分钟)

#### 步骤1：打包应用文件
```bash
# 在210服务器或通过208远程执行
cd /data/nfs_share/html/HOME_PROD/
tar -czf /tmp/hyqm_prod_files.tar.gz hyqm_prod/

# 传输到208服务器Web目录
cp /tmp/hyqm_prod_files.tar.gz /usr/share/nginx/html/transfer/
```

#### 步骤2：下载并部署文件
```bash
# 腾讯云下载
wget -O /tmp/hyqm_prod_files.tar.gz https://main.51zsqc.com/transfer/hyqm_prod_files.tar.gz

# 创建目录结构
mkdir -p /mnt/datadisk0/apps/java-web/hyqm-prod/{conf,logs,scripts}
mkdir -p /mnt/datadisk0/volumns/java-web/hyqm-prod/{workdir,ui,logs}

# 解压应用文件
tar -xzf /tmp/hyqm_prod_files.tar.gz -C /tmp/
cp -r /tmp/hyqm_prod/java/* /mnt/datadisk0/volumns/java-web/hyqm-prod/workdir/
cp -r /tmp/hyqm_prod/vue/* /mnt/datadisk0/volumns/java-web/hyqm-prod/ui/
cp -r /tmp/hyqm_prod/logs/* /mnt/datadisk0/volumns/java-web/hyqm-prod/logs/
```

### 4.3 阶段三：容器配置和部署 (预计15分钟)

#### 步骤1：创建Docker Compose配置
```yaml
# /mnt/datadisk0/apps/java-web/hyqm-prod/docker-compose.yml
version: '3.8'

networks:
  1panel-network:
    external: true

services:
  hyqm-prod:
    image: 4525e4716f8b
    container_name: hyqm-prod
    networks:
      - 1panel-network
    volumes:
      - /mnt/datadisk0/volumns/java-web/hyqm-prod/workdir:/workdir
      - /mnt/datadisk0/volumns/java-web/hyqm-prod/logs:/home/<USER>/logs
      - /mnt/datadisk0/volumns/java-web/hyqm-prod/ui:/home/<USER>/projects/ruoyi-ui
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=prod
      - DB_HOST=mysql-prod
      - DB_PORT=3306
      - REDIS_HOST=redis-prod
      - REDIS_PORT=6379
    restart: unless-stopped
    ports:
      - "8081:80"  # 避免与imppc的8080冲突
```

#### 步骤2：配置应用参数
```bash
# 更新数据库连接配置
# 需要检查workdir中的application.yml或application.properties
# 确保数据库连接指向mysql-prod容器
```

#### 步骤3：启动服务
```bash
cd /mnt/datadisk0/apps/java-web/hyqm-prod/
docker compose up -d

# 检查启动状态
docker ps | grep hyqm-prod
docker logs hyqm-prod --tail 50
```

### 4.4 阶段四：验证和清理 (预计10分钟)

#### 步骤1：功能验证
```bash
# API接口测试
curl -I http://localhost:8081/admin-api/infra/config/list

# 前端页面测试
curl -I http://localhost:8081/

# 数据库连接测试
docker exec mysql-prod mysql -u pearproject -p'kG7#tPq9@zR2$vX1' \
  -e "USE hyrm; SELECT COUNT(*) FROM infra_config;"
```

#### 步骤2：清理临时文件
```bash
# 208服务器清理
rm /usr/share/nginx/html/transfer/hyrm.sql.tar.gz
rm /usr/share/nginx/html/transfer/hyqm_prod_files.tar.gz
rm /tmp/hyrm.sql

# 腾讯云服务器清理
rm /tmp/hyrm.sql.tar.gz /tmp/hyrm.sql
rm /tmp/hyqm_prod_files.tar.gz
rm -rf /tmp/hyqm_prod/

# 容器内清理
docker exec mysql-prod rm /tmp/hyrm.sql /tmp/hyrm_clean.sql
```

## 5. 验收标准

### 5.1 数据库验收
- [ ] hyrm数据库成功创建
- [ ] 所有表结构完整导入
- [ ] 数据记录数量一致
- [ ] pearproject用户权限正确

### 5.2 应用验收
- [ ] 容器成功启动
- [ ] 应用日志无错误
- [ ] API接口响应正常
- [ ] 前端页面可访问
- [ ] 数据库连接正常

### 5.3 性能验收
- [ ] 响应时间在可接受范围
- [ ] 内存使用正常
- [ ] CPU使用正常
- [ ] 网络连接稳定

## 6. 回滚方案

### 6.1 回滚触发条件
- 数据库导入失败
- 应用启动失败
- 功能验证失败
- 性能严重下降

### 6.2 回滚步骤
1. 停止新容器：`docker compose down`
2. 删除新数据库：`DROP DATABASE hyrm;`
3. 恢复K8s服务访问
4. 清理迁移文件
5. 记录问题和原因

## 7. 时间安排

### 7.1 预计总时间：135分钟 (2小时15分钟)
- 数据库迁移：90分钟 (26.3GB大文件)
- 文件迁移：20分钟
- 容器部署：15分钟
- 验证清理：10分钟

### 7.2 建议执行时间
- 业务低峰期执行
- 预留回滚时间窗口
- 通知相关用户

## 8. 注意事项

### 8.1 关键配置检查
- 数据库连接字符串
- Redis连接配置
- RabbitMQ队列配置
- 文件上传路径配置

### 8.2 依赖服务确认
- mysql-prod容器运行正常
- redis-prod容器运行正常
- rabbitmq-prod容器运行正常
- 1panel-network网络正常

### 8.3 监控要点
- 容器启动日志
- 数据库连接状态
- API接口响应
- 前端页面加载

这个迁移计划基于标准化操作手册和hyrm-imppc的成功经验制定，确保hyqm_prod的平稳迁移。
