---
date_created: 2025-07-23 15:45:00
date_modified: 2025-07-23 15:45:00
author: 赵王飞
---

# Dify 1.6.0 升级计划

## 背景
当前腾讯云服务器运行Dify v1.4.1，需要升级到最新的v1.6.0版本。根据官方发布信息，v1.6.0带来了重大的流程优化和Model Context Protocol (MCP)标准支持。

## 版本对比分析

### 当前版本：v1.4.1
- 运行状态：正常
- 部署位置：`/mnt/datadisk0/volumns/dify/dify/docker/`
- 访问端口：280 (HTTP), 2443 (HTTPS)
- 容器状态：所有服务正常运行

### 目标版本：v1.6.0
- 发布时间：2025年7月10日
- 主要特性：
  - 引入Model Context Protocol (MCP)标准
  - 重大流程优化和性能提升
  - 移除之前的性能瓶颈
  - 增强模型输入输出的一致性和兼容性
  - 更流畅高效的集成和扩展能力

## 升级风险评估

### 高风险项
1. **数据库结构变更**：可能需要数据迁移
2. **配置文件格式变更**：docker-compose.yaml和.env文件可能需要更新
3. **插件系统变更**：MCP标准可能影响现有插件
4. **API接口变更**：可能影响现有集成

### 中风险项
1. **容器镜像更新**：新版本镜像可能有依赖变更
2. **环境变量变更**：新增或修改的环境变量
3. **网络配置变更**：端口或网络策略调整

### 低风险项
1. **UI界面更新**：前端界面优化
2. **性能优化**：后台性能提升

## 升级前准备工作

### 1. 完整备份
```bash
# 备份当前运行的完整目录
cd /mnt/datadisk0/volumns
tar -czf dify_v1.4.1_backup_$(date +%Y%m%d_%H%M%S).tar.gz dify/

# 备份数据库
docker exec docker-db-1 pg_dump -U postgres dify > dify_db_backup_$(date +%Y%m%d_%H%M%S).sql

# 备份volumes数据
cp -r /mnt/datadisk0/volumns/dify/dify/docker/volumes /tmp/dify_volumes_backup_$(date +%Y%m%d_%H%M%S)
```

### 2. 环境信息收集
```bash
# 记录当前容器状态
docker compose ps > current_containers_status.txt

# 记录当前配置
cp docker-compose.yaml docker-compose.yaml.v1.4.1.bak
cp .env .env.v1.4.1.bak

# 记录当前镜像版本
docker images | grep dify > current_images.txt
```

### 3. 依赖检查
- 确认Docker和Docker Compose版本兼容性
- 检查系统资源使用情况
- 验证网络连接和端口可用性

## 当前环境状态

### Git仓库信息
- 当前分支：main
- 当前版本：1.4.1
- 可用版本标签：1.5.0, 1.5.1, 1.6.0
- 仓库状态：有未提交的修改（docker-compose.yaml）
- 未跟踪文件：.env.backup, docker-compose.yaml.backup.7.5等

### 现有配置文件
- docker-compose.yaml（已修改）
- .env（运行配置）
- 多个备份文件（.backup, .bak等）

## 升级执行方案

### 方案A：原地升级（推荐）

#### 步骤1：备份当前状态
```bash
cd /mnt/datadisk0/volumns/dify/dify/docker

# 备份当前配置
cp docker-compose.yaml docker-compose.yaml.v1.4.1.$(date +%s).bak
cp .env .env.v1.4.1.$(date +%s).bak

# 备份整个目录
cd /mnt/datadisk0/volumns
tar -czf dify_v1.4.1_backup_$(date +%Y%m%d_%H%M%S).tar.gz dify/

# 备份数据库
docker exec docker-db-1 pg_dump -U postgres dify > /tmp/dify_db_backup_$(date +%Y%m%d_%H%M%S).sql
```

#### 步骤2：停止当前服务
```bash
cd /mnt/datadisk0/volumns/dify/dify/docker
docker compose down
```

#### 步骤3：更新代码和配置
```bash
# 保存当前修改
cd /mnt/datadisk0/volumns/dify/dify
git stash push -m "backup current changes before upgrade to 1.6.0"

# 获取最新标签并切换到1.6.0
git fetch --tags
git checkout 1.6.0

# 进入docker目录
cd docker

# 比较配置文件差异
echo "=== Docker Compose 差异 ==="
diff docker-compose.yaml.v1.4.1.*.bak docker-compose.yaml || true

echo "=== 环境变量差异 ==="
diff .env.v1.4.1.*.bak .env.example || true

# 合并配置（需要手动处理）
echo "请根据上述差异手动更新.env文件"
```

#### 步骤4：配置文件更新
```bash
# 复制现有的.env配置作为基础
cp .env.v1.4.1.*.bak .env

# 检查新版本的环境变量示例
cat .env.example

# 手动更新.env文件，添加新的必需变量
# 重点关注：
# - MCP相关配置
# - 新增的插件配置
# - 性能优化相关参数
nano .env
```

#### 步骤5：数据迁移（如需要）
```bash
# 先启动数据库和基础服务
docker compose up -d db redis

# 等待数据库就绪
sleep 10

# 检查是否需要数据库迁移
docker compose run --rm api flask db current

# 如果需要迁移，执行升级
docker compose run --rm api flask db upgrade

# 检查插件迁移（如果从1.0之前版本升级）
# docker compose run --rm api flask migrate-plugin-data
```

#### 步骤6：启动新版本服务
```bash
# 启动所有服务
docker compose up -d

# 等待服务启动
sleep 30
```

#### 步骤7：验证升级结果
```bash
# 检查容器状态
docker compose ps

# 检查服务健康状态
curl -I http://localhost:280

# 检查API健康状态
curl http://localhost:280/health

# 检查各服务日志
docker compose logs api | tail -20
docker compose logs web | tail -20
docker compose logs worker | tail -20

# 检查新功能是否可用
echo "请通过Web界面验证："
echo "1. 用户登录功能"
echo "2. 应用创建和运行"
echo "3. 工作流功能"
echo "4. 新的MCP功能（如果适用）"
```

### 方案B：并行部署（安全方案）

#### 步骤1：创建新的部署目录
```bash
cd /mnt/datadisk0/volumns
git clone https://github.com/langgenius/dify.git dify_v1.6.0
cd dify_v1.6.0
git checkout 1.6.0
```

#### 步骤2：配置新环境
```bash
cd docker
# 复制现有配置
cp /mnt/datadisk0/volumns/dify/dify/docker/.env .
# 修改端口避免冲突（如使用281端口）
sed -i 's/280:80/281:80/g' docker-compose.yaml
sed -i 's/2443:443/2444:443/g' docker-compose.yaml
```

#### 步骤3：数据迁移
```bash
# 复制volumes数据
cp -r /mnt/datadisk0/volumns/dify/dify/docker/volumes ./
```

#### 步骤4：启动新版本
```bash
docker compose up -d
```

#### 步骤5：验证和切换
```bash
# 验证新版本正常运行
curl -I http://localhost:281

# 如果正常，停止旧版本
cd /mnt/datadisk0/volumns/dify/dify/docker
docker compose down

# 调整新版本端口为标准端口
cd /mnt/datadisk0/volumns/dify_v1.6.0/docker
sed -i 's/281:80/280:80/g' docker-compose.yaml
sed -i 's/2444:443/2443:443/g' docker-compose.yaml
docker compose down && docker compose up -d
```

## 回滚方案

### 快速回滚
```bash
# 停止新版本
cd /mnt/datadisk0/volumns/dify_v1.6.0/docker  # 或当前升级目录
docker compose down

# 恢复备份
cd /mnt/datadisk0/volumns
rm -rf dify
tar -xzf dify_v1.4.1_backup_YYYYMMDD_HHMMSS.tar.gz

# 启动原版本
cd dify/dify/docker
docker compose up -d
```

### 数据回滚
```bash
# 如果需要恢复数据库
docker exec -i docker-db-1 psql -U postgres -d dify < dify_db_backup_YYYYMMDD_HHMMSS.sql
```

## 升级后验证清单

### 功能验证
- [ ] 用户登录正常
- [ ] 应用创建和编辑功能
- [ ] 工作流功能正常
- [ ] 模型调用正常
- [ ] 插件功能正常
- [ ] API接口响应正常

### 性能验证
- [ ] 响应时间对比
- [ ] 内存使用情况
- [ ] CPU使用情况
- [ ] 数据库性能

### 新功能验证
- [ ] MCP标准功能测试
- [ ] 新增的优化功能
- [ ] 界面更新验证

## 时间安排

### 准备阶段（1小时）
- 备份当前环境
- 环境信息收集
- 升级方案最终确认

### 执行阶段（2-3小时）
- 停止服务
- 代码更新
- 配置调整
- 数据迁移
- 服务启动

### 验证阶段（1小时）
- 功能验证
- 性能测试
- 问题排查

### 总计：4-5小时

## 注意事项

1. **升级时机**：建议在业务低峰期进行
2. **监控准备**：升级过程中密切监控系统状态
3. **通知机制**：提前通知相关用户可能的服务中断
4. **文档更新**：升级完成后更新相关文档
5. **团队协调**：确保技术团队在升级期间可用

## 应急联系

- 技术负责人：赵王飞
- 备用方案：如遇重大问题立即回滚
- 升级窗口：预计4-5小时
- 最大容忍中断时间：2小时

## 升级执行记录

### 执行时间
- 开始时间：2025-07-23 16:09
- 完成时间：2025-07-23 16:30
- 总耗时：约21分钟

### 执行步骤记录

#### 1. 备份阶段（16:09-16:11）
```bash
# 配置文件备份
cp docker-compose.yaml docker-compose.yaml.v1.4.1.1753258158.bak
cp .env .env.v1.4.1.1753258158.bak

# 完整目录备份
sudo tar -czf dify_v1.4.1_backup_20250723_161036.tar.gz dify/
# 备份大小：581M

# 数据库备份
docker exec docker-db-1 pg_dump -U postgres dify > /tmp/dify_db_backup_20250723_161150.sql
# 备份大小：324M
```

#### 2. 服务停止（16:11）
```bash
docker compose down
# 所有容器已成功停止
```

#### 3. 版本切换（16:12）
```bash
# 保存当前修改
git stash push -m "backup current changes before upgrade to 1.6.0"

# 切换到1.6.0版本
git checkout 1.6.0
# 当前版本：1.6.0 (390e4cc0b)
```

#### 4. 配置更新（16:13-16:14）
- 复制1.4.1配置作为基础
- 添加1.6.0新增配置项：
  - CELERY_SENTINEL_PASSWORD=
  - ALLOW_UNSAFE_DATA_SCHEME=false
  - RESPECT_XFORWARD_HEADERS_ENABLED=false
  - QUEUE_MONITOR_THRESHOLD=200

#### 5. 数据库迁移（16:15-16:27）
```bash
# 启动数据库和Redis
docker compose up -d db redis

# 数据库迁移
docker compose run --rm api flask db upgrade
# 迁移成功："Database migration successful!"
```

#### 6. 服务启动（16:28-16:30）
```bash
docker compose up -d
# 所有服务启动成功
```

### 升级结果验证

#### 容器状态
```
NAME                     IMAGE                                       STATUS
docker-api-1             langgenius/dify-api:1.6.0                   Up
docker-db-1              postgres:15-alpine                          Up (healthy)
docker-nginx-1           nginx:latest                                Up
docker-plugin_daemon-1   langgenius/dify-plugin-daemon:0.1.3-local   Up
docker-redis-1           redis:6-alpine                              Up (healthy)
docker-sandbox-1         langgenius/dify-sandbox:0.2.12              Up (healthy)
docker-ssrf_proxy-1      ubuntu/squid:latest                         Up
docker-weaviate-1        semitechnologies/weaviate:1.19.0            Up
docker-web-1             langgenius/dify-web:1.6.0                   Up
docker-worker-1          langgenius/dify-api:1.6.0                   Up
```

#### 服务健康检查
- HTTP状态：307 Temporary Redirect → /apps ✅
- Web界面：正常加载 ✅
- API服务：gunicorn 23.0.0 正常运行 ✅
- Worker服务：celery worker 正常连接 ✅
- 数据库：PostgreSQL 连接正常 ✅

#### 新功能确认
- 插件守护进程：langgenius/dify-plugin-daemon:0.1.3-local ✅
- MCP相关配置：已添加到环境变量 ✅
- 性能优化：新版本容器正常运行 ✅

### 发现的问题

#### 时区配置问题
**问题描述**：
- 系统时区：CST（东八区）- Wed Jul 23 04:42:38 PM CST 2025
- 容器时区：UTC - Wed Jul 23 08:42:38 UTC 2025
- 时差：容器时间比系统时间快4小时（应该慢8小时）

**影响范围**：
- 日志时间戳显示不正确
- 定时任务可能在错误时间执行
- 用户界面显示时间与本地时间不匹配

**解决方案**：需要在docker-compose.yaml中为所有服务添加时区环境变量

**解决过程**：
1. 在.env文件中添加时区配置：
   ```bash
   LOG_TZ=Asia/Shanghai
   TZ=Asia/Shanghai
   ```

2. 在docker-compose.yaml的共享环境变量中添加TZ配置：
   ```yaml
   x-shared-env: &shared-api-worker-env
     # ... 其他配置
     LOG_TZ: ${LOG_TZ:-UTC}
     TZ: ${TZ:-UTC}
   ```

3. 为Web服务单独添加TZ环境变量：
   ```yaml
   web:
     environment:
       # ... 其他配置
       TZ: ${TZ:-UTC}
   ```

4. 重启所有服务应用配置

**解决结果**：
- 系统时间：Wed Jul 23 04:48:42 PM CST 2025
- API容器时间：Wed Jul 23 16:48:42 CST 2025 ✅
- Web容器时间：Wed Jul 23 16:49:13 CST 2025 ✅
- Worker容器时间：Wed Jul 23 16:48:42 CST 2025 ✅
- 所有容器时区已统一为东八区（CST）

## 升级决策

**推荐方案**：方案A（原地升级）
**原因**：
1. 操作相对简单
2. 资源占用较少
3. 配置迁移更直接
4. 有完整备份保障

**备选方案**：方案B（并行部署）
**适用场景**：
1. 对稳定性要求极高
2. 需要详细测试新版本
3. 有充足的存储空间
4. 可以接受更长的升级时间