---
date_created: 2025-07-17 14:14:14
date_modified: 2025-07-17 14:14:14
author: 赵王飞
---

接下来要执行数据文件备份,备份过程中需要清理掉无意义的日志等文件
```
# 先进入 html 目录, 先查找所有下级目录统计有多少个log文件, 然后移除掉所有 log 文件
cd /data/nfs_share/html/
find . -name "*.log" -type f | wc -l  # 统计log文件数量
find . -name "*.log" -type f -delete  # 删除所有log文件


```


工商银行相关任务清理
```

cd /data/nfs_share/html/icbc_naoer/icbc


```



# web 配置

## icbc
### naoer
#### 雷池
https://naoer.icbc.51zsqc.com/
naoer.icbc.51zsqc.com
http://127.0.0.1:20031
淖尔e钱包
#### 容器配置
##### bridge

##### web


### 原始配置
naoer.php
```php
const DB_HOST = "************";
const DB_NAME = "icbc_naoer";
const DB_USER = "icbc_naoer";
const DB_PWD = "7D51A5B1992D@h*";
const DB_PORT = "3306";
const DB_LOG_HOST = "************";
const DB_LOG_NAME = "icbc_naoer_log";
const DB_LOG_USER = "icbc_naoer";
const DB_LOG_PWD = "7D51A5B1992D@h*";
const DB_LOG_PORT = "3306";
const REDIS_HOST = "************";
const REDIS_PORT = 30079;
define("JAVA_HOSTS", "************:31802");
```

需要使用sed命令
把数据库 IP、Redis、Javahosts ip统一替换为***********
```bash
sed -i 's/************/***********/g' naoer.php
sed -i 's/************/***********/g' naoer.php
sed -i 's/30079/6379/g' naoer.php
sed -i 's/31802/22222/g' naoer.php
```

```sql
# 创建yinji用户，使用Vmdc登陆
CREATE USER 'icbc_naoer' @'%' IDENTIFIED BY '7D51A5B1992D@h*';
# 授予yinji用户yinji_db数据库所有操作权限，并使用密码Vmdc
GRANT ALL PRIVILEGES ON `icbc_naoer`.* TO 'icbc_naoer' @'%' IDENTIFIED BY '7D51A5B1992D@h*';
# 刷新数据库权限
FLUSH PRIVILEGES;
```

### 赤峰那仁泰

https://cf.icbc.51zsqc.com/
cf.icbc.51zsqc.com
http://127.0.0.1:20032
赤峰那仁泰
#### 容器配置
##### bridge
测试地址
https://cf.icbc.51zsqc.com/h5/
https://cf.icbc.51zsqc.com/admin.php
##### web

### 赤峰那仁泰



数据库迁移
```sql
show create database icbc_chifeng;
> 
CREATE DATABASE `icbc_chifeng` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */
CREATE DATABASE `icbc_chifeng_log` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */
```

## 配置文件调整
cf.php
```php
const DB_HOST = "************";
const DB_NAME = "icbc_chifeng";
const DB_USER = "icbc_chifeng";
const DB_PWD = "zKCbPFayLmM6nZnl";
const DB_PORT = "3306";

const DB_LOG_HOST = "************";
const DB_LOG_NAME = "icbc_chifeng_log";
const DB_LOG_USER = "icbc_chifeng";
const DB_LOG_PWD = "zKCbPFayLmM6nZnl";
const DB_LOG_PORT = "3306";
const REDIS_HOST = "************";
const REDIS_PORT = 30079;
define("JAVA_HOSTS", "************:30454");
```

```bash
sed -i 's/************/***********/g' cf.php
sed -i 's/************/***********/g' cf.php
sed -i 's/30079/6379/g' cf.php
sed -i 's/30454/22222/g' cf.php
```

创建数据库和用户
```sql
-- 创建数据库
CREATE DATABASE `icbc_chifeng` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */
CREATE DATABASE `icbc_chifeng_log` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */

-- 创建用户
CREATE USER 'icbc_chifeng' @'%' IDENTIFIED BY 'zKCbPFayLmM6nZnl';
GRANT ALL PRIVILEGES ON `icbc_chifeng`.* TO 'icbc_chifeng' @'%' IDENTIFIED BY 'zKCbPFayLmM6nZnl';
GRANT ALL PRIVILEGES ON `icbc_chifeng_log`.* TO 'icbc_chifeng' @'%' IDENTIFIED BY 'zKCbPFayLmM6nZnl';
FLUSH PRIVILEGES;
```



### Busybox容器持久运行方法

为了让Busybox容器持久运行，而不是在命令执行完毕后立即退出，可以使用以下几种方法：

1.  **使用 `tail -f /dev/null`**
    这是最常用的方法之一。通过让容器执行一个持续运行的命令，可以使其保持活跃状态。`/dev/null` 是一个特殊的设备文件，所有写入它的数据都会被丢弃。`tail -f` 命令会持续追踪文件末尾的变化，但由于 `/dev/null` 不会产生新的内容，所以 `tail -f /dev/null` 会一直运行而不会占用太多资源。

    ```bash
    docker run -itd --name my_busybox busybox tail -f /dev/null
    ```

    -   `-i`: 保持标准输入打开，即使没有连接到终端。
    -   `-t`: 分配一个伪TTY。
    -   `-d`: 后台运行容器。
    -   `--name my_busybox`: 为容器指定一个名称。
    -   `busybox`: 镜像名称。
    -   `tail -f /dev/null`: 容器启动后执行的命令。

2.  **使用 `sleep infinity`**
    这是一个更直接且意图明确的方法，尤其是在较新版本的Busybox中。`sleep infinity` 命令会使进程无限期地休眠，从而使容器保持运行状态。

    ```bash
    docker run -itd --name my_busybox_sleep busybox sleep infinity
    ```

    此命令的参数与 `tail -f /dev/null` 的用法类似，只是将启动命令替换为 `sleep infinity`。

3.  **运行一个简单的后台服务**
    如果你的Busybox容器需要运行一个实际的服务（例如一个简单的HTTP服务器），那么容器自然会保持运行，只要该服务还在运行。

    ```bash
    # 示例：运行一个简单的HTTP服务器 (Busybox可能需要安装httpd或类似工具)
    # docker run -itd --name my_busybox_http -p 8080:80 busybox httpd -f
    ```

选择哪种方法取决于你的具体需求。对于仅仅需要一个“挂起”的容器以便后续进入执行命令的情况，`tail -f /dev/null` 或 `sleep infinity` 是非常方便和轻量级的选择。


