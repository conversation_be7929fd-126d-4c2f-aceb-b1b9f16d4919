# PHP应用容器化架构设计文档

## 1. 架构概述

本文档描述了将传统PHP应用迁移到Docker容器化环境的架构设计，采用数据与配置分离的现代化部署模式。

## 2. 设计理念

### 2.1 核心原则
- **数据与配置分离**：应用配置与业务数据完全分离存储
- **安全优先**：移除不必要的端口暴露，配置文件隔离
- **标准化管理**：参考1Panel管理模式，建立统一的应用管理规范
- **可扩展性**：为未来添加更多应用预留架构空间

### 2.2 架构转变
```
传统模式：
├── /usr/share/nginx/html/
    ├── 应用代码
    ├── 配置文件 (暴露风险)
    └── 临时文件 (安全隐患)

容器化模式：
├── /mnt/datadisk0/apps/        # 应用配置管理
└── /mnt/datadisk0/volumns/     # 数据存储
```

## 3. 目录架构设计

### 3.1 整体结构
```
/mnt/datadisk0/
├── apps/                       # 应用配置目录
│   └── php_home_208/           # 具体应用配置
│       ├── conf/               # 配置文件
│       │   ├── apache2/        # Apache配置
│       │   └── php/            # PHP配置(预留)
│       ├── logs/               # 日志文件
│       ├── docker-compose.yml  # 容器编排
│       ├── .env               # 环境变量
│       ├── manage.sh          # 管理脚本
│       └── README.md          # 应用文档
└── volumns/                   # 数据存储目录
    └── php_home_208/          # 应用数据
        ├── pearproject/       # 项目1数据
        ├── pearproject_admin/ # 项目2数据
        └── pearproject_api/   # 项目3数据
```

### 3.2 设计优势
1. **配置集中管理**：所有配置文件统一存放在apps目录
2. **数据安全隔离**：业务数据与配置完全分离
3. **日志独立存储**：便于监控和问题排查
4. **版本控制友好**：配置文件可独立进行版本管理

## 4. 安全改进措施

### 4.1 网络安全
- **移除端口映射**：容器不直接暴露端口到公网
- **内部网络通信**：使用1panel-network进行容器间通信
- **反向代理访问**：通过1Panel OpenResty统一入口

### 4.2 文件安全
- **配置文件隔离**：从web根目录移除所有配置文件
- **临时文件清理**：移除压缩包等临时文件
- **权限控制**：合理设置文件和目录权限

## 5. 容器配置

### 5.1 网络配置
```yaml
networks:
  1panel-network:
    external: true
```

### 5.2 卷挂载
```yaml
volumes:
  - /mnt/datadisk0/volumns/php_home_208:/var/www/html
  - /mnt/datadisk0/apps/php_home_208/conf/apache2/default.conf:/etc/apache2/sites-enabled/000-default.conf
  - /mnt/datadisk0/apps/php_home_208/logs:/var/log/apache2
```

## 6. 管理工具

### 6.1 管理脚本功能
- `start` - 启动容器
- `stop` - 停止容器  
- `restart` - 重启容器
- `status` - 查看状态
- `logs` - 查看日志
- `shell` - 进入容器
- `remove` - 删除容器

### 6.2 使用示例
```bash
# 查看应用状态
./manage.sh status

# 重启应用
./manage.sh restart
```

## 7. 扩展规划

### 7.1 多应用支持
```
/mnt/datadisk0/apps/
├── php_home_208/      # 当前应用
├── php_home_209/      # 未来应用
└── shared_configs/    # 共享配置
```

### 7.2 配置共享机制
- 通过软链接实现配置文件共享
- 建立共享配置模板
- 支持应用间配置继承

## 8. 监控与维护

### 8.1 日志管理
- Apache访问日志：JSON格式，便于分析
- 错误日志：独立存储，便于问题排查
- 容器日志：通过docker logs查看

### 8.2 备份策略
- 配置文件：定期备份apps目录
- 业务数据：定期备份volumns目录
- 数据库：独立备份策略

## 9. 最佳实践

### 9.1 部署流程
1. 创建应用配置目录
2. 准备配置文件
3. 部署业务数据
4. 启动容器服务
5. 配置反向代理

### 9.2 维护建议
- 定期检查容器状态
- 监控日志文件大小
- 及时清理临时文件
- 保持配置文件版本控制

## 10. 总结

本架构设计实现了：
- ✅ 安全性提升：移除端口暴露，配置隔离
- ✅ 管理标准化：统一的目录结构和管理工具
- ✅ 可扩展性：支持多应用部署
- ✅ 维护便利性：清晰的日志和监控机制

该架构为后续的应用迁移和扩展提供了坚实的基础。
