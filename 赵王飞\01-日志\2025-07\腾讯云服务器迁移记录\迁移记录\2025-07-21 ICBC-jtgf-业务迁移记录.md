# “玖泰e钱包” (jtgf) 业务迁移记录

**迁移日期**: 2025-07-21

## 1. 业务背景

- **业务名称**: 玖泰e钱包 (jtgf)
- **目标**: 将业务从传统环境迁移至 Docker 容器化环境，使用命令行进行管理，并详细记录过程。

## 2. 迁移计划

### 2.1. 原始配置信息

从 `/mnt/datadisk0/volumns/icbc/jtgf/web/env/jtgf.php` 文件中获取的原始配置如下：

```php
const DB_HOST = "************";
const DB_NAME = "icbc_jtgf";
const DB_USER = "icbc_jtgf";
const DB_PWD = "F@tmzR&zTwZV";

const REDIS_HOST = "************";
const REDIS_PORT = 30079;

define("JAVA_HOSTS", "************:31804");
```

### 2.2. 执行步骤

#### 步骤一：创建 Docker 容器

- **创建 `bridge` 容器**
- **创建 `web` 容器**

#### 步骤二：修改配置文件 (`jtgf.php`)

- 备份原文件
- 修改 JavaBridge、数据库、Redis 的连接地址

#### 步骤三：创建数据库及授权

- 在 `mysql-prod` 容器内创建数据库和用户，并授予权限。

#### 步骤四：调整目录权限

- 为 `runtime`, `icbc`, `sftp_temp` 目录添加写权限。

#### 步骤五：修改 `Jssdk.php`

- 清空 `get_sign_package` 和 `get_card_sign_package` 方法的返回内容。

---

## 3. 执行记录

### 3.1. 创建 Docker 容器

```bash
# 创建 bridge 容器
docker run -d --name icbc-jtgf-bridge --network 1panel-network --restart=always -v /mnt/datadisk0/volumns/icbc/jtgf/bridge:/workdir java:8-jre java -jar /workdir/JavaBridge.jar HTTP:22222

# 创建 web 容器
docker run -d --name icbc-jtgf-web --network 1panel-network --restart=always -p 20034:80 -v /docker-data/web/php.ini:/etc/php/7.2/apache2/php.ini -v /docker-data/web/apache_default.conf:/etc/apache2/sites-enabled/default.conf -v /mnt/datadisk0/volumns/icbc/jtgf/web:/var/www -v /etc/localtime:/etc/localtime zhao0829wang/apache2-php:7.2-rc4 apache2-foreground
```

### 3.2. 修改配置文件

```bash
# 进入目录、备份并执行修改
cd /mnt/datadisk0/volumns/icbc/jtgf/web/env/ && \
cp jtgf.php jtgf.php.bak && \
sed -i 's/define("JAVA_HOSTS", "************:31804");/define("JAVA_HOSTS", "icbc-jtgf-bridge:22222");/' jtgf.php && \
sed -i 's/const DB_HOST = "************";/const DB_HOST = "mysql-prod";/' jtgf.php && \
sed -i 's/const DB_LOG_HOST = "************";/const DB_LOG_HOST = "mysql-prod";/' jtgf.php && \
sed -i 's/const REDIS_HOST = "************";/const REDIS_HOST = "redis-prod";/' jtgf.php && \
sed -i 's/const REDIS_PORT = 30079;/const REDIS_PORT = 6379;/' jtgf.php
```

### 3.3. 创建数据库及授权

```bash
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' -e "CREATE DATABASE IF NOT EXISTS \`icbc_jtgf\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin; CREATE DATABASE IF NOT EXISTS \`icbc_jtgf_log\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin; CREATE USER IF NOT EXISTS 'icbc_jtgf'@'%' IDENTIFIED BY 'F@tmzR&zTwZV'; GRANT ALL PRIVILEGES ON \`icbc_jtgf\`.* TO 'icbc_jtgf'@'%'; GRANT ALL PRIVILEGES ON \`icbc_jtgf_log\`.* TO 'icbc_jtgf'@'%'; FLUSH PRIVILEGES;"
```

### 3.4. 调整目录权限

```bash
chmod -R a+w /mnt/datadisk0/volumns/icbc/jtgf/web/runtime /mnt/datadisk0/volumns/icbc/jtgf/web/icbc /mnt/datadisk0/volumns/icbc/jtgf/web/application/icbc/service/sftp_temp
```

### 3.5. 修改 Jssdk.php

```bash
# 经检查，/mnt/datadisk0/volumns/icbc/jtgf/web/application/icbc/service/ 目录下不存在 Jssdk.php 文件。
# 与 zjgx 业务不同，此步骤不适用于 jtgf 业务迁移，无需执行任何操作。
```

### 3.6. 更新业务域名

根据业务要求，将域名从 `jtgf.5iyinji.cn` 更新为 `jtgf.icbc.51zsqc.com`。

```bash
# 进入配置目录、备份并修改域名
cd /mnt/datadisk0/volumns/icbc/jtgf/web/env/ && \
cp loader.php loader.php.bak.domain_update && \
cp jtgf.php jtgf.php.bak.domain_update && \
sed -i 's/"jtgf.5iyinji.cn"/"jtgf.icbc.51zsqc.com"/' loader.php && \
sed -i 's/const SECURE_HOST = "jtgf.5iyinji.cn";/const SECURE_HOST = "jtgf.icbc.51zsqc.com";/' jtgf.php
```

查看web容器日志
```bash
docker exec -it icbc-jtgf-web tail -f /var/log/apache2/access.log
docker exec -it icbc-jtgf-web tail -f /var/log/apache2/error.log
```


