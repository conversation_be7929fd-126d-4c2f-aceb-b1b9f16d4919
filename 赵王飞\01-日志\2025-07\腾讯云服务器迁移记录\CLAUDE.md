# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 项目概述

这是一个腾讯云服务器迁移项目的文档库，记录了从旧服务器集群（200.1.66.x）迁移到腾讯云服务器的完整过程。项目采用Docker容器化架构，使用1Panel作为管理面板，实现现代化的服务器基础设施。

**重要提醒**: 文档中的信息大部分为AI生成，不一定绝对正确，请以实际操作为准。当前主要任务是迁移汇远融媒/汇远轻媒1.0相关业务，已完成部分迁移工作。

## 核心架构

### 服务器环境
- **操作系统**: Ubuntu 24.04 LTS / TencentOS Server 4
- **容器技术**: Docker CE + Docker Compose
- **管理面板**: 1Panel
- **网络架构**: 基于`1panel-network`的容器化网络
- **存储结构**: 数据盘挂载到`/mnt/datadisk0/`

### 关键服务容器
- `mysql-prod`: MySQL 5.7.44 生产数据库
- `redis-prod`: Redis 8.0.3 缓存服务
- `apache_php_service`: Apache2 + PHP 7.2 Web服务
- `1Panel-openresty-YFc6`: OpenResty反向代理网关
- 雷池WAF: Web应用防火墙，作为所有外部请求的入口

### 真实网络架构
```
外部请求 → 雷池WAF → {
    ├─ 直接转发到容器 (部分请求)
    └─ 转发到1Panel OpenResty → 容器 (部分请求)
}
```

### 网络架构原则
- 所有外部访问必须先通过雷池WAF防护
- 雷池WAF内置OpenResty，可直接转发请求到容器或转发给1Panel OpenResty
- 容器间通信使用`1panel-network`网络，通过容器名称（如`mysql-prod`）而非IP地址

## 目录结构

```
/mnt/datadisk0/
├── apps/           # 应用配置管理
│   ├── php-web/    # PHP Web应用配置
│   ├── database/   # 数据库配置
│   └── shared/     # 共享配置
├── volumns/        # 数据存储
│   ├── php_home_208/ # PHP应用数据
│   └── shared/     # 共享数据
├── docker/         # Docker数据
├── backups/        # 备份文件
└── logs/          # 日志文件
```

## 常用命令

### Docker管理
```bash
# 查看容器状态
docker ps -a

# 查看容器日志
docker logs [容器名]

# 进入容器
docker exec -it [容器名] /bin/bash

# 重启容器
docker restart [容器名]
```

### 数据库操作
```bash
# 连接生产数据库
docker exec -it mysql-prod mysql -u root -p

# 备份数据库
docker exec mysql-prod mysqldump -u root -p[密码] [数据库名] > backup.sql

# 导入数据库
docker cp backup.sql mysql-prod:/tmp/
docker exec mysql-prod mysql -u root -p[密码] [数据库名] < /tmp/backup.sql
```

### 文件传输流程
使用HTTP直传模式进行服务器间文件传输：
1. 中转服务器（************）打包文件到`/usr/share/nginx/html/transfer/`
2. 目标服务器使用`wget https://main.51zsqc.com/transfer/[文件名]`下载
3. 下载完成后立即删除中转文件

## 容器化管理规范

### 命名规范
- **应用实例**: `[项目名]-[环境]` (如 `pearproject-prod`)
- **容器名称**: `[应用类型]-[实例名]` (如 `php-web-pearproject-prod`)
- **网络名称**: 主网络使用`1panel-network`，应用网络使用`[应用类型]-network`

### 安全原则
- 禁止直接暴露端口到公网
- 所有外部访问必须先通过雷池WAF防护
- 雷池WAF根据规则决定直接转发到容器或通过1Panel OpenResty转发
- 使用容器名称进行内部服务通信
- 敏感配置通过.env文件管理

### 标准目录结构
每个应用实例包含：
- `conf/` - 配置文件
- `logs/` - 日志文件  
- `scripts/` - 管理脚本
- `docker-compose.yml` - 容器编排
- `.env` - 环境变量

## 迁移流程标准

### 应用迁移步骤
1. 在旧服务器备份应用文件和数据库
2. 通过HTTP直传方式传输到新服务器
3. 创建容器化环境配置
4. 修改配置文件使用容器服务名
5. 启动容器服务
6. 在雷池WAF中配置转发规则
7. 配置1Panel OpenResty反向代理（如需要）
8. 验证服务可用性

### 数据库迁移步骤
1. 在中转服务器执行`mysqldump -h ************ ...`
2. 压缩并上传到Web目录
3. 新服务器下载并导入到`mysql-prod`容器
4. 清理临时文件

## 环境凭证

### 旧环境
- 服务器: ************ (中转), ************ (数据库源)
- 数据库Root密码: `TxkjDB2020#`

### 新环境  
- 数据库容器: `mysql-prod`
- 数据库Root密码: `56d9DavJ*zwrwj9rmA`
- 业务数据库用户: `pearproject` / `kG7#tPq9@zR2$vX1`

## 监控与维护

### 日常检查
- 容器运行状态: `docker ps`
- 磁盘空间: `df -h`
- 内存使用: `free -h`
- 服务日志: `docker logs [容器名]`

### 备份策略
- 配置备份: `/mnt/datadisk0/apps/` 目录
- 数据备份: `/mnt/datadisk0/volumns/` 目录
- 数据库备份: mysqldump导出
- 镜像备份: 关键镜像打包保存

这个迁移项目体现了现代化的容器化基础设施建设，通过标准化的流程和规范确保服务的稳定性和可维护性。