---
date_created: 2025-07-23 08:11:17
date_modified: 2025-07-23 08:11:18
author: 赵王飞
---
# 背景
目前我们服务器的docker 环境已经基本配置好了，容器之间互相访问，使用对方的名称，例如mysql-prod,可以正常访问. 为了让我们本地开发电脑能够访问服务端环境, 我们搭建了一个 openvpn, 连接到了服务端的网络组可以通过 IP 正常访问以上数据库等， 但是在调试过程中发现似乎容器的 IP 并非固定的, 而是有可能变化的. 这样我们本地如果使用 IP 进行访问, 就可能无法访问到或访问到错误的节点. 

## 需求确认

- **核心目标**: 本地开发电脑通过 OpenVPN 连接到服务器后，能够像在服务器上一样，直接使用 Docker 容器的名称（如 `mysql-test`, `rabbitmq-test`）来访问对应的服务。
- **痛点**: 解决因容器 IP 地址不固定而导致本地开发配置频繁变更的问题，提升开发效率和稳定性。
- **技术要求**: 实现对 Docker 内部 DNS 解析的桥接，使得 VPN 客户端可以利用该桥接服务解析容器名。

## 初始环境信息

- **服务器操作系统**: Ubuntu
- **Docker 网络**:
  - **网络名称**: `1panel-network`
  - **网段**: `**********/16`
  - **网关**: `**********`
- **Docker 内部 DNS**:
  - **地址**: `127.0.0.11` (由 Docker 守护进程提供，仅在容器内可达)
- **关键容器**:
  - `mysql-test`: 业务数据库，IP: `***********` (示例)
  - `rabbitmq-test`: 消息队列服务
  - `1Panel-openvpn-sNji`: OpenVPN 服务容器，IP: `**********`

# 解决方案：部署 DNS 桥接服务

为了从根本上解决本地开发环境无法通过容器名访问服务的问题，我们决定采用在服务器上部署一个轻量级 DNS 服务的方案。该服务将作为“桥梁”，将来自 VPN 客户端的 DNS 查询转发给 Docker 的内部 DNS 服务。

## 实施过程与问题排查

### 阶段一：部署 DNS 服务 (dnsmasq)

我们选择使用 `dnsmasq` 作为一个轻量级的 DNS 转发器，并以 Docker 容器的形式运行。

- **初始部署命令**:
  ```bash
  docker run -d --name dns-bridge --network 1panel-network -p 53:53/udp -p 53:53/tcp --cap-add=NET_ADMIN janeczku/go-dnsmasq --server=127.0.0.11 --listen=0.0.0.0:53
  ```

- **遇到的问题**: 容器启动失败，报错信息提示端口 `53` 已被占用。
  - **原始错误信息**:
    ```
    docker: Error response from daemon: failed to set up container networking: driver failed programming external connectivity on endpoint dns-bridge (...): failed to bind host port for 0.0.0.0:53:***********:53/tcp: address already in use
    ```

### 阶段二：排查端口占用

为了找出哪个进程占用了 DNS 的标准端口 `53`，我们执行了以下诊断命令。

- **执行的诊断命令**:
  ```bash
  sudo netstat -tulpn | grep :53
  ```

- **诊断结果**:
  - **原始数据**:
    ```
    tcp        0      0 **********:53           0.0.0.0:*               LISTEN      63694/systemd-resol 
    tcp        0      0 **********:53           0.0.0.0:*               LISTEN      63694/systemd-resol 
    udp        0      0 **********:53           0.0.0.0:*                           63694/systemd-resol 
    udp        0      0 **********:53           0.0.0.0:*                           63694/systemd-resol 
    ```
  - **分析**: 结果明确指向了系统服务 `systemd-resolved`。这是 Ubuntu 等现代 Linux 系统内置的 DNS 解析服务，其“存根监听器 (Stub Listener)” 功能默认占用了 `53` 端口。

### 阶段三：释放 53 端口

为了让我们的 `dns-bridge` 容器能够使用 `53` 端口，需要先禁用 `systemd-resolved` 的存根监听器。

- **已执行的修改命令**:
  ```bash
  sudo sed -i -E 's/^#?(DNSStubListener=).*/\1no/' /etc/systemd/resolved.conf
  ```

- **后续步骤：使配置生效**
  1.  **更新系统 DNS 指向**：为了确保服务器自身网络正常，将 `/etc/resolv.conf` 指向 `systemd-resolved` 管理的真实解析器。
      ```bash
      sudo ln -sf /run/systemd/resolve/resolv.conf /etc/resolv.conf
      ```
  2.  **重启服务**：应用所有修改。
      ```bash
      sudo systemctl restart systemd-resolved
      ```
  3.  **验证端口释放**：再次检查 53 端口，预期无任何输出。
      ```bash
      sudo netstat -tulpn | grep :53
      ```
      命令执行后无输出，确认端口已成功释放。

### 阶段四：重新部署并调试 DNS 服务

端口释放后，我们再次尝试部署 `dns-bridge` 容器，并在此过程中解决了一系列新的问题。

1.  **问题：容器名冲突**
    - **原因**：首次部署失败后，留下了一个停止状态的同名容器。
    - **解决**：先删除旧容器。
      ```bash
      docker rm dns-bridge
      ```

2.  **问题：DNS 查询失败 (参数错误)**
    - **现象**：成功部署 `dns-bridge` 后，使用 `dig @127.0.0.1 mysql-test` 命令测试，查询无返回结果。
    - **排查过程**:
      1.  **安装诊断工具**：为确保 `dig` 命令可用，安装 `dnsutils`。
          ```bash
          sudo apt-get update && sudo apt-get install -y dnsutils
          ```
      2.  **检查容器日志**：发现容器启动失败，日志打印出帮助文档。
          ```bash
          docker logs dns-bridge
          ```
      3.  **定位原因**：发现初始部署命令中，用于指定上游 DNS 的参数 `--server` 是错误的，正确的参数应为 `--nameservers`。

3.  **最终部署**
    - **清理错误容器**：强制删除配置错误的容器。
      ```bash
      docker rm -f dns-bridge
      ```
    - **执行正确命令**：使用正确的 `--nameservers` 参数重新部署。
      ```bash
      docker run -d --name dns-bridge --network 1panel-network -p 53:53/udp -p 53:53/tcp --cap-add=NET_ADMIN janeczku/go-dnsmasq --nameservers=127.0.0.11 --listen=0.0.0.0:53
      ```

4.  **服务验证**
    - **检查日志**：确认新容器正常启动。
      ```bash
      docker logs dns-bridge
      ```
      日志显示 `Ready for queries on tcp://0.0.0.0:53`，服务正常。
    - **执行 `dig` 测试**：在服务器本地进行最终验证。
      ```bash
      dig @127.0.0.1 mysql-test
      ```
    - **验证成功**：`dig` 命令成功返回 `mysql-test` 的内部 IP `***********`，证明 DNS 桥接服务工作正常。

### 阶段五：配置 OpenVPN 推送 DNS

最后一步是将我们的 DNS 桥接服务地址推送给所有 VPN 客户端。

1.  **定位 OpenVPN 网关**：OpenVPN 容器连接在 `1panel-network` 网络，其网关地址为 `**********`，该地址即为服务器主机在该网络中的地址，也是我们 DNS 服务的监听地址。

2.  **修改 OpenVPN 配置**：通过 `sacli` 工具，将 DNS 服务器地址推送到客户端。
    ```bash
    # 设置主 DNS 服务器为我们的 dns-bridge 服务
    docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --key "vpn.server.dhcp_option.dns.1" --value "**********" ConfigPut

    # 设置备用 DNS 为公共 DNS
    docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --key "vpn.server.dhcp_option.dns.2" --value "***************" ConfigPut
    ```

3.  **重启 OpenVPN 服务**：使配置生效。
    ```bash
    docker restart 1Panel-openvpn-sNji
    ```

# 最终修正方案：基于 Hosts 文件的权威 DNS 服务

上一个 `--hosts` 参数方案因容器启动失败而告终。日志显示，`go-dnsmasq` 镜像**不存在** `--hosts` 参数，我犯了严重的技术性错误。正确的参数是 `--hostsfile`，它要求提供一个文件路径。

- **最终方案原理**
  - 我们将在服务器的宿主机上创建一个临时的 `hosts` 文件，其中包含所有内部容器的 IP 和名称映射。
  - 我们将这个文件挂载到 `dns-bridge` 容器内部。
  - `dns-bridge` 启动时，使用 `--hostsfile` 参数加载这个文件，从而成为一个针对内部容器的权威 DNS 服务器。
  - 对于文件中不存在的域名（如公网站点），请求将被转发到公共 DNS 服务器。

- **实施步骤**
  1.  **清理失败的服务**: 停止并删除因参数错误而启动失败的 `dns-bridge` 容器。
  2.  **在宿主机创建 hosts 文件**: 将之前获取的容器 IP 和名称，写入服务器上的一个临时文件（例如 `/tmp/dns-hosts`）。
  3.  **部署最终服务**: 执行最终的 `docker run` 命令，该命令包含：
      -   使用 `-v` 参数将宿主机的 `/tmp/dns-hosts` 文件挂载到容器的 `/etc/hosts.dnsmasq`。
      -   使用 `--hostsfile` 参数指向容器内的 `/etc/hosts.dnsmasq` 文件。
  4.  **客户端验证**: 客户端无需任何配置，直接 `ping 容器名` 即可。

# 最终成果

经过多次排查和修正，最终基于 `--hostsfile` 的权威 DNS 服务已成功部署。服务器端的配置现已全部完成。

**最终验证步骤：**

1.  **客户端配置**: 确保本地 OpenVPN 客户端的 `.ovpn` 配置文件中，**没有** `block-outside-dns` 这一行。
2.  **连接 VPN**: 连接到 OpenVPN。
3.  **测试解析**: 在本地电脑的命令行中，执行 `ping mysql-test`。
4.  **测试外网**: 同时，执行 `ping www.baidu.com` 以确保外网访问正常。

**预期结果**:
- `ping mysql-test` 应该成功返回容器的 IP 地址 `***********`。
- `ping www.baidu.com` 应该成功返回百度的 IP 地址。

至此，本地开发电脑在连接 OpenVPN 后，应能够像在服务器上一样，通过容器名称直接访问所有 Docker 服务，且不影响正常的互联网访问。

# 最终方案探索与验证

在经历了多次失败的尝试后，我们最终确定了问题的核心：**dns-bridge 服务本身工作正常，但客户端未能使用其作为 DNS 服务器。** 为了在执行最终的永久性修复前获得无可辩驳的证据，我们采取了“客户端先行”的验证策略。

## 客户端先行验证

此步骤旨在证明，只要客户端的 DNS 查询能正确地到达 `dns-bridge` 服务的真实 IP 地址，问题就能解决。

- **验证操作**: 在本地 Windows 电脑上，连接 VPN 后，执行以下命令，强制使用 `dns-bridge` 的真实 IP (`***********`) 进行查询。
  ```powershell
  nslookup mysql-test ***********
  ```

- **验证结果**: 
  ```
  服务器:  UnKnown
  Address:  ***********

  非权威应答:
  名称:    mysql-test
  Address:  ***********
  ```

- **结论**: **验证完全成功。** 此结果无可辩驳地证明了，客户端与 `dns-bridge` 服务之间的网络是通畅的，`dns-bridge` 服务本身是健康、可用且配置正确的。我们面临的唯一问题，就是如何让客户端自动、永久地使用这个正确的 DNS 服务。

## 永久性修复方案

基于客户端验证的成功，我们制定了以下一劳永逸的修复方案，旨在建立一个正确、健壮且不会因 IP 地址变动而失效的系统。

### 阶段一：服务器端修正 (待实施)

1.  **重建 `dns-bridge` 服务并固化 IP**
    - **目的**: 为 DNS 服务分配一个永久、固定的 IP 地址，杜绝未来因容器重启导致 IP 变化而引发的一切问题。
    - **操作**:
      1.  删除当前的 `dns-bridge` 容器。
      2.  使用 `--ip=************` 参数，并结合已知正确的 `--hostsfile` 和文件挂载配置，重新创建 `dns-bridge` 服务。

2.  **重建 `OpenVPN` 服务并推送正确 DNS**
    - **目的**: 强制 OpenVPN 服务器，将我们新建的、地址永久固定的 DNS 服务，自动推送给每一个连接上来的客户端。
    - **操作**:
      1.  删除当前的 `1Panel-openvpn-sNji` 容器。
      2.  使用 `--dns=************` 参数重新创建 OpenVPN 服务。

### 方案二：使用网关IP作为DNS服务器（失败）

在“固定IP方案”因网络限制失败后，我们提出了一个新假设：利用Docker的端口映射，将网络网关地址(`**********`)作为稳定的DNS服务器地址推送给客户端。

- **验证操作**: 在`dns-bridge`容器启动并监听53端口后，在本地Windows电脑上执行以下命令，强制使用网关IP进行查询。
  ```powershell
  nslookup mysql-test **********
  ```

- **验证结果**:
  ```
  DNS request timed out.
      timeout was 2 seconds.
  服务器:  UnKnown
  Address:  **********
  *** 请求 UnKnown 超时
  ```

- **结论**: **验证彻底失败。** 此结果证明，Docker的端口映射机制，并不会自动应用于发往其所在网络网关地址的流量。该方案在技术上不可行。

## 问题探索与方案演进

### 第一轮探索：DNS桥接方案

**初始思路**：创建DNS桥接服务，为容器名称提供稳定的IP解析。

**遇到的问题**：
1. **动态IP问题**：`dns-bridge`容器的IP地址仍然是动态分配的
2. **网络限制**：发现`1panel-network`无法指定固定IP（详情参见知识库文档：[[为什么Docker网络无法指定固定IP？]]）

**关键发现**：现有网络环境的根本限制使得DNS桥接方案无法获得稳定的服务地址。

### 第二轮探索：网络隔离与固定IP方案

**改进思路**：创建专用网络，为DNS服务分配固定IP地址。

**实施步骤**：
1. 创建`dns-network`(**********/16)专用网络
2. 为`dns-bridge`分配固定IP `**********54`
3. 配置OpenVPN推送DNS和路由信息

**验证结果**：
- ✅ 服务器内部DNS解析正常：`dig @**********54 mysql-test` 返回 `***********`
- ❌ 客户端DNS查询超时：`nslookup mysql-test **********54` 失败

**根因分析**：虽然配置了路由推送，但OpenVPN的网络转发机制存在限制，客户端无法访问同网络中的其他容器。

### 第三轮探索：统一固定IP网络方案

**新思路**：将所有业务容器迁移到统一的固定IP网络，通过hosts文件解析。

**可行性验证过程**：

#### 验证点1：网络基础连通性 ✅
- **测试**：客户端ping **********（OpenVPN在新网络中的IP）
- **结果**：成功，延迟32-46ms
- **结论**：VPN客户端能够访问新网段

#### 验证点2：容器双网络连接 ✅  
- **测试**：为`mysql-test`容器分配固定IP ***********
- **结果**：容器成功获得双网络连接
  - 原网络：*********** (1panel-network)
  - 新网络：*********** (dns-network)
- **结论**：双网络连接技术可行

#### 验证点3：跨网络服务通信 ❌
- **测试**：客户端ping ***********（mysql-test的新IP）
- **结果**：请求超时
- **关键发现**：客户端无法访问服务器内网IP（如***********）

### 核心问题识别

通过三轮探索，我们识别出了问题的本质：

**OpenVPN路由转发限制**：
- 客户端只能访问到OpenVPN容器本身（**********）
- 无法访问同网络中的其他容器（***********）
- 无法访问服务器宿主机的内网IP

这表明OpenVPN的网络机制**只提供到OpenVPN容器的连接**，而不具备完整的**网络桥接和转发能力**。

### 方案收敛：回归简单可靠

基于以上探索和问题识别，我们得出结论：

1. **复杂网络方案的根本障碍**：OpenVPN的路由转发限制使得所有基于网络层的解决方案都面临技术瓶颈
2. **简单方案的可行性**：既然网络层方案受限，应该回归到应用层解决方案
3. **最优解**：动态IP映射同步方案（详见：[[2025-07-23 动态IP映射同步方案]]）

## 经验总结

### 技术层面
1. **网络复杂性**：Docker网络、VPN路由、容器间通信的组合比预期更复杂
2. **环境限制**：现有的`1panel-network`和OpenVPN配置存在不可绕过的技术限制
3. **验证的重要性**：分阶段验证避免了大量无效工作

### 方法论层面
1. **从简单到复杂再回归简单**：复杂方案往往引入更多故障点
2. **问题本质识别**：技术问题需要追根溯源，找到根本原因
3. **可行性优先**：在理想方案和可行方案之间，优先选择可行方案

### 决策过程
1. **渐进式探索**：每个方案都基于前一个方案的问题和发现
2. **实证验证**：通过实际测试验证理论假设
3. **及时调整**：当发现根本性障碍时，果断调整方向

## OpenVPN网络转发机制深度分析结果

### 分析过程

经过对OpenVPN Access Server网络转发机制的深度分析，我们发现了问题的根本原因。详细分析过程记录在：[[2025-07-23 OpenVPN网络转发机制深度分析]]

### 关键发现

#### 技术突破
1. **部分修复OpenVPN防火墙**：通过将`private_access`从`nat`改为`route`模式，客户端现在能收到OpenVPN的回复
2. **识别架构限制**：OpenVPN容器无法与dns-network中的任何其他容器通信
3. **确认根本原因**：OpenVPN Access Server的架构设计阻止容器间直接通信

#### 原始验证数据
**修复前客户端测试**：
```
PS C:\Users\<USER>\Documents\hyxx_doc> ping ***********
请求超时。
请求超时。
```

**修复后客户端测试**：
```
PS C:\Users\<USER>\Documents\hyxx_doc> ping ***********
来自 172.27.236.3 的回复: 无法访问目标主机。
请求超时。

*********** 的 Ping 统计信息:
    数据包: 已发送 = 2，已接收 = 1，丢失 = 1 (50% 丢失)，
```

**网络接口统计验证**：
```bash
# OpenVPN容器ping前后eth1接口统计完全无变化
eth1:    2609      44    0    0    0     0          0         0     2096      32    0    0    0     0       0          0
eth1:    2609      44    0    0    0     0          0         0     2096      32    0    0    0     0       0          0
```

### 根本原因确认

**OpenVPN Access Server架构限制**：
- OpenVPN被设计为安全的VPN网关，所有通信必须通过VPN隧道
- 内置多层网络隔离机制，防止容器间直接通信
- 这是架构设计的核心特性，不是配置问题

### 最终技术决策

#### ❌ 放弃网络层面解决方案
- **统一固定IP网络方案**：与OpenVPN架构设计根本冲突
- **DNS桥接方案**：无法绕过OpenVPN的网络隔离限制

#### ✅ 确定最终方案
**[[2025-07-23 动态IP映射同步方案]]** 是唯一可行且可靠的解决方案：
- 完全绕过网络层面的限制
- 不依赖OpenVPN的网络转发能力
- 架构兼容性强，实用性高

### 执行记录

#### 选择原因
经过深度技术分析，确认OpenVPN Access Server的架构设计从根本上阻止了容器间直接通信，网络层面的解决方案在技术上不可行。

#### 实施内容
1. **OpenVPN配置优化**：将`private_access`改为`route`模式（部分改善了连通性）
2. **架构限制确认**：通过网络接口统计等技术手段确认了根本限制
3. **方案决策**：基于技术分析结果，确定采用动态IP映射同步方案

#### 关键参数配置
- OpenVPN配置：`"vpn.server.routing.private_access": "route"`
- 网络测试结果：客户端50%丢包率，说明网络路径部分打通但目标不可达
- 技术验证：OpenVPN容器网络接口统计显示无数据包发送

---