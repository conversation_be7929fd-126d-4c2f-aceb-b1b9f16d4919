# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Structure

This repository contains daily logs and project documentation organized by date, with several distinct areas:

### Key Directory Structure
- `2025-07/` - Main directory containing current logs and projects
- `zhihuidangjian-research/` - Node.js research project for platform analysis
- `腾讯云服务器配置/` - Tencent Cloud server configuration documentation
- `.kiro/specs/` - Project specifications and design documents

### Primary Project: zhihuidangjian-research

This is a Node.js project for researching and analyzing the "智慧党建" (Smart Party Building) platform.

**Architecture:**
- `test-access.js` - Main entry point for testing platform access
- `browser-manager.js` - Puppeteer-based browser automation manager
- `mcp-research-tool.js` - Research tool with logging and analysis capabilities
- `config.js` - Configuration for target URLs, credentials, and strategies
- `调研发现报告.md` - Detailed research findings report

**Technology Stack:**
- Node.js with <PERSON><PERSON>pet<PERSON> for browser automation
- Screenshot capture and analysis
- Anti-bot detection and bypass strategies
- Structured logging and reporting

## Common Development Commands

### zhihuidangjian-research Project
```bash
# Navigate to project directory
cd zhihuidangjian-research

# Install dependencies
npm install

# Run the main test
npm test
# or
npm start
# or
node test-access.js

# Run specific components
node mcp-research-tool.js
node browser-manager.js
```

### Key Configuration
The project uses a multi-strategy approach to handle anti-bot mechanisms:
- Direct access attempts
- Header modification strategies  
- Step-by-step navigation
- Wait and retry mechanisms

## Research Methodology

The zhihuidangjian-research project implements a comprehensive platform analysis approach:

1. **Automated Access Layer** - Uses Puppeteer to attempt programmatic access
2. **Anti-Bot Detection** - Identifies and attempts to bypass anti-crawling measures
3. **Screenshot Documentation** - Captures visual evidence of platform features
4. **Manual Analysis Integration** - Combines automated and manual research methods
5. **Structured Reporting** - Generates detailed findings in markdown format

## Development Notes

### Security Considerations
- Project handles login credentials for authorized research
- Implements responsible access patterns to avoid server overload
- Includes anti-bot detection to understand platform security measures

### File Organization
- Screenshots are organized by category (login/, main-interface/, modules/, technical/)
- Research findings are documented in detailed markdown reports
- Configuration is centralized in config.js for easy modification

### Browser Automation Best Practices
- Uses realistic user-agent strings and headers
- Implements delays between requests
- Monitors network requests and responses
- Captures console output for debugging

## Project Specifications

The `.kiro/specs/` directory contains detailed project documentation:
- `zhihuidangjian-platform-research/` - Complete research project specifications
- `tencent-cloud-migration/` - Server migration project documentation
- `multi-server-vpn-access/` - VPN and multi-server access configurations

## Testing and Debugging

To test the research tools:
1. Ensure Node.js and npm are installed
2. Navigate to `zhihuidangjian-research/`
3. Run `npm install` to install dependencies
4. Execute `npm test` to run the access tests
5. Check `screenshots/` directory for captured images
6. Review console output for debugging information

The browser manager includes comprehensive logging for troubleshooting access issues and anti-bot detection results.