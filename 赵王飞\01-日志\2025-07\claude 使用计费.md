---
date_created: 2025-07-25 13:58:31
date_modified: 2025-07-25 13:58:32
author: 赵王飞
---

Total cost:            $0.84
Total duration (API):  3m 52.6s
Total duration (wall): 2h 1m 29.1s
Total code changes:    318 lines added, 0 lines removed
Usage by model:
    claude-3-5-haiku:  69.6k input, 505 output, 0 cache read, 0 cache write
       claude-sonnet:  89 input, 7.6k output, 185.3k cache read, 158.5k cache write, 2 web search

Total cost:            $1.29
Total duration (API):  3m 19.0s
Total duration (wall): 12m 39.9s
Total code changes:    77 lines added, 6 lines removed
Usage by model:
    claude-3-5-haiku:  2.7k input, 151 output, 0 cache read, 0 cache write
       claude-sonnet:  115 input, 6.5k output, 618.3k cache read, 267.8k cache write
```
  "mcpServers": {
    "mcp-ssh-tencent": {
      "type": "stdio",
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@fangjunjie/ssh-mcp-server",
        "--host",
        "***************",
        "--port",
        "22",
        "--username",
        "ubuntu",
        "--password",
        "*uuXf6!t2Y97g2Kki2"
      ],
      "env": {}
    },
    "ai-interaction": {
      "command": "D:/GreenSoft/mcp-interactive.exe",
      "args": [
        "run",
        "--transport",
        "stdio",
        "--ui",
        "pyqt"
      ],
      "env": {}
    },
    "mcp-ssh-208": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@fangjunjie/ssh-mcp-server",
        "--host",
        "************",
        "--port",
        "22",
        "--username",
        "root",
        "--password",
        "Zsqc12#$"
      ]
    },
    "mcp-ssh-210": {
      "command": "cmd",
      "args": [
        "/c",
        "npx",
        "-y",
        "@fangjunjie/ssh-mcp-server",
        "--host",
        "************",
        "--port",
        "22",
        "--username",
        "root",
        "--password",
        "Zsqc12#$"
      ]
    }
  }
```