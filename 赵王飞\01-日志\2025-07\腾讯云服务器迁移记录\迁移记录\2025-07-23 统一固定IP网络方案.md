---
date_created: 2025-07-23 11:33:00
date_modified: 2025-07-23 11:33:00
author: Cascade
tags: [Docker, 网络, 固定IP, VPN, 容器管理]
related_docs: ["2025-07-23 本地通过容器名称访问服务器问题解决.md"]
---

# 统一固定IP网络方案

## 方案概述

### 背景
经过前期的DNS桥接方案探索，我们发现该方案在网络连通性方面存在复杂的技术障碍。为了彻底解决"本地通过容器名称访问服务器问题"，我们决定采用一个更加简单、直接、可靠的方案：**将所有业务容器迁移到统一的固定IP网络，通过hosts文件实现名称解析**。

### 核心思路
- **统一网络管理**：利用已创建的`dns-network`(**********/16)作为所有业务容器的统一网络
- **固定IP分配**：为每个业务容器分配固定的、可预测的IP地址
- **Hosts文件解析**：在客户端通过hosts文件直接映射容器名到固定IP，绕过DNS解析的复杂性
- **双网络连接**：容器同时连接原网络和新网络，确保服务间通信不受影响

### 方案优势

| 优势 | 说明 |
|------|------|
| **简单可靠** | 无需复杂的DNS服务，hosts文件解析更稳定 |
| **完全可控** | 我们掌控所有IP分配，不受外部网络限制 |
| **易于维护** | 添加新服务只需分配IP并更新hosts文件 |
| **高度兼容** | 不影响现有服务间的通信方式 |
| **故障排除简单** | 问题定位和解决更加直接 |

## 实施计划

### 第一阶段：网络基础验证
**目标**：确认网络基础设施可用性

1. **确认网络状态**
   - 验证`dns-network`(**********/16)可用性
   - 检查OpenVPN容器在新网络中的连接状态

2. **规划IP分配**
   - 为现有业务容器制定固定IP分配表
   - 预留IP段用于未来扩展

### 第二阶段：容器逐步迁移
**目标**：安全地将业务容器迁移到新网络

1. **逐个迁移容器**
   - 将业务容器连接到`dns-network`并分配固定IP
   - 保持原有网络连接，确保服务不中断

2. **验证双网络连接**
   - 确保容器同时连接新旧网络
   - 测试容器间通信正常

3. **测试服务可用性**
   - 验证迁移后服务功能正常
   - 检查服务性能无明显下降

### 第三阶段：客户端配置
**目标**：配置客户端实现通过容器名访问

1. **配置OpenVPN路由**
   - 确保VPN客户端能访问**********/16网段
   - 验证路由推送正常工作

2. **更新客户端hosts文件**
   - 添加容器名到固定IP的映射
   - 测试名称解析正常

3. **验证端到端连通性**
   - 测试客户端能否通过容器名访问服务
   - 验证业务功能完整性

## 可行性验证点

### 🔍 验证点1：网络基础连通性
- **验证内容**：客户端能否访问**********/16网段
- **验证方法**：在客户端ping **********（OpenVPN在新网络中的IP）
- **预期结果**：ping成功，延迟正常
- **风险评估**：如果失败，说明OpenVPN路由推送有问题
- **验证命令**：
  ```bash
  ping **********
  ```

### 🔍 验证点2：容器双网络连接
- **验证内容**：容器能否同时连接到1panel-network和dns-network
- **验证方法**：选择一个测试容器，连接到dns-network并分配固定IP，检查网络接口
- **预期结果**：容器拥有两个网络接口，分别对应两个网络的IP
- **风险评估**：如果失败，可能影响现有服务间通信
- **验证命令**：
  ```bash
  # 连接容器到新网络
  docker network connect --ip *********** dns-network <test-container>
  
  # 检查容器网络接口
  docker exec <test-container> ip addr show
  ```

### 🔍 验证点3：跨网络服务通信
- **验证内容**：连接到新网络的容器能否与仍在旧网络的容器通信
- **验证方法**：从新网络容器访问旧网络容器的服务端口
- **预期结果**：通信正常，服务响应正常
- **风险评估**：如果失败，可能导致业务中断
- **验证命令**：
  ```bash
  # 从新网络容器测试访问旧网络容器
  docker exec <new-network-container> curl -I http://***********:3306
  ```

### 🔍 验证点4：客户端hosts文件解析
- **验证内容**：客户端通过hosts文件能否正确解析容器名
- **验证方法**：在客户端hosts文件添加测试条目，使用ping验证解析
- **预期结果**：容器名能正确解析为指定IP
- **风险评估**：如果失败，说明hosts文件配置有误
- **验证步骤**：
  1. 编辑 `C:\Windows\System32\drivers\etc\hosts`
  2. 添加测试条目：`*********** test-container`
  3. 执行：`ping test-container`

### 🔍 验证点5：端到端业务访问
- **验证内容**：客户端能否通过容器名访问业务服务
- **验证方法**：在客户端通过容器名访问数据库或web服务
- **预期结果**：业务功能正常，响应时间可接受
- **风险评估**：如果失败，需要检查网络路由或服务配置
- **验证示例**：
  ```bash
  # 测试数据库连接
  mysql -h mysql-test -u root -p
  
  # 测试web服务
  curl http://hyqm2-prod/
  ```

## IP分配规划

### 网络配置
- **统一网络**：`dns-network` (**********/16)
- **网关地址**：**********
- **可用IP范围**：********** - **************
- **业务容器IP段**：**********0 - ***********
- **预留IP段**：**********00 - **********99（未来扩展）

### 容器IP分配表

| 容器名称 | 当前IP(1panel-network) | 新分配IP(dns-network) | 服务端口 | 备注 |
|---------|----------------------|---------------------|---------|------|
| mysql-prod | ********** | **********0 | 3306 | 生产数据库 |
| mysql-test | *********** | **********1 | 3306 | 测试数据库 |
| rabbitmq-prod | ********** | **********0 | 5672,15672 | 消息队列 |
| hyqm2-prod | *********** | *********** | 80 | 汇远轻媒 |
| icbc-chifeng-web | ********** | *********** | 80 | 工行赤峰 |
| icbc-naoer-bridge | *********** | *********** | 8080 | 工行呼伦贝尔桥接 |
| icbc-jtgf-web | *********** | *********** | 80 | 工行交通罚款 |
| icbc-jtgf-bridge | *********** | *********** | 8080 | 工行交通罚款桥接 |

### 特殊IP分配
| IP地址         | 用途         | 备注         |
| ------------ | ---------- | ---------- |
| **********   | 网关         | Docker网络网关 |
| **********   | OpenVPN容器  | 已分配        |
| **********54 | DNS服务（已废弃） | 原DNS桥接方案遗留 |

## 客户端配置

### Windows hosts文件配置
**文件路径**：`C:\Windows\System32\drivers\etc\hosts`

```
# Docker容器固定IP映射 - 2025-07-23
# 数据库服务
**********0 mysql-prod
**********1 mysql-test

# 消息队列
**********0 rabbitmq-prod

# Web应用
*********** hyqm2-prod
*********** icbc-chifeng-web
*********** icbc-jtgf-web

# 桥接服务
*********** icbc-naoer-bridge
*********** icbc-jtgf-bridge
```

### Linux/Mac hosts文件配置
**文件路径**：`/etc/hosts`

```bash
# Docker容器固定IP映射 - 2025-07-23
**********0 mysql-prod
**********1 mysql-test
**********0 rabbitmq-prod
*********** hyqm2-prod
*********** icbc-chifeng-web
*********** icbc-naoer-bridge
*********** icbc-jtgf-web
*********** icbc-jtgf-bridge
```

## 实施脚本

### 容器迁移脚本
```bash
#!/bin/bash
# 容器迁移到固定IP网络脚本

# 定义容器和IP映射
declare -A CONTAINERS=(
    ["mysql-prod"]="**********0"
    ["mysql-test"]="**********1"
    ["rabbitmq-prod"]="**********0"
    ["hyqm2-prod"]="***********"
    ["icbc-chifeng-web"]="***********"
    ["icbc-naoer-bridge"]="***********"
    ["icbc-jtgf-web"]="***********"
    ["icbc-jtgf-bridge"]="***********"
)

# 迁移函数
migrate_container() {
    local container_name=$1
    local fixed_ip=$2
    
    echo "正在迁移容器: $container_name -> $fixed_ip"
    
    # 连接到新网络并分配固定IP
    docker network connect --ip $fixed_ip dns-network $container_name
    
    if [ $? -eq 0 ]; then
        echo "✅ $container_name 成功连接到 dns-network ($fixed_ip)"
        
        # 验证网络连接
        docker exec $container_name ip addr show | grep $fixed_ip
        if [ $? -eq 0 ]; then
            echo "✅ IP地址验证成功"
        else
            echo "❌ IP地址验证失败"
        fi
    else
        echo "❌ $container_name 连接失败"
    fi
    
    echo "---"
}

# 执行迁移
for container in "${!CONTAINERS[@]}"; do
    migrate_container $container ${CONTAINERS[$container]}
done

echo "迁移完成！"
```

### 验证脚本
```bash
#!/bin/bash
# 网络连通性验证脚本

echo "=== 网络连通性验证 ==="

# 验证点1：基础网络连通性
echo "1. 测试基础网络连通性..."
ping -c 3 **********
if [ $? -eq 0 ]; then
    echo "✅ 基础网络连通性正常"
else
    echo "❌ 基础网络连通性异常"
fi

# 验证点2：容器IP连通性
echo "2. 测试容器IP连通性..."
CONTAINER_IPS=("**********0" "**********1" "**********0" "***********")

for ip in "${CONTAINER_IPS[@]}"; do
    ping -c 1 -W 2 $ip > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ $ip 连通正常"
    else
        echo "❌ $ip 连通异常"
    fi
done

echo "验证完成！"
```

## 风险控制

### 主要风险点
1. **服务中断风险**：容器迁移过程中可能导致短暂服务中断
2. **网络冲突风险**：双网络连接可能导致路由冲突
3. **性能影响风险**：额外的网络层可能影响性能
4. **配置错误风险**：IP分配错误可能导致服务无法访问

### 风险缓解措施
1. **分阶段实施**：逐个容器迁移，降低整体风险
2. **保持双连接**：迁移期间保持原网络连接
3. **充分测试**：每个阶段都进行充分的功能测试
4. **回滚准备**：制定详细的回滚方案

### 回滚方案
如果迁移过程中出现问题，可以通过以下步骤回滚：

```bash
# 断开容器与新网络的连接
docker network disconnect dns-network <container-name>

# 验证容器仍在原网络中正常工作
docker exec <container-name> ip addr show
```

## 预期效果

### 功能效果
- ✅ 客户端可通过容器名直接访问服务器容器
- ✅ IP地址固定，不再受容器重启影响
- ✅ 配置简单，维护方便
- ✅ 性能稳定，延迟可控

### 维护效果
- 📝 新增服务只需分配IP并更新hosts文件
- 🔧 故障排除更加直接和简单
- 📊 网络拓扑清晰，便于监控和管理
- 🔄 扩展性好，支持未来业务增长

## 后续优化

### 自动化改进
1. **自动IP分配**：开发脚本自动分配和管理IP
2. **配置同步**：自动同步hosts文件到多个客户端
3. **健康检查**：定期检查网络连通性和服务可用性

### 监控告警
1. **网络监控**：监控容器网络连接状态
2. **性能监控**：监控网络延迟和吞吐量
3. **故障告警**：及时发现和通知网络异常

---

**文档状态**：待实施  
**创建时间**：2025-07-23  
**负责人**：系统管理员  
**预计完成时间**：2025-07-24
