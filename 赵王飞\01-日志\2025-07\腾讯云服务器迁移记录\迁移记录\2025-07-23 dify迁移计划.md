---
date_created: 2025-07-23 14:15:19
date_modified: 2025-07-23 15:40:00
author: 赵王飞
---

# 背景
我们需要将旧的208服务器的dify服务迁移到新的腾讯云上, 需要保留之前的配置. 所以我们首先需要了解208服务器的dify相关情况, 先完成数据迁移, 然后再进行腾讯云上的容器重建.
在腾讯云上不单单需要重现,因为当前使用的是 dify1.4版本, dify 发布了最新的1.6x版本, 我们还需要进行升级.

# 迁移执行过程

## 阶段一：源服务器调研和备份

### 1. 定位Dify安装目录
- 在208服务器上搜索docker-compose.yaml文件
- 发现Dify安装在 `/data/dify/dify/` 目录
- 确认完整的目录结构包含：
  - docker配置文件（docker-compose.yaml等）
  - 环境配置文件（.env等）
  - nginx配置
  - 证书管理
  - 数据卷volumes
  - 中间件配置

### 2. 创建完整备份
- 执行命令：`tar -czf /data/dify_complete.tar.gz /data/dify/`
- 备份文件大小：560MB
- 包含完整的Dify安装目录结构

### 3. 文件传输
- 将备份文件复制到web传输目录：`/usr/share/nginx/html/transfer/`
- 通过wget方式下载到腾讯云：`wget https://main.51zsqc.com/transfer/dify_complete.tar.gz`

## 阶段二：腾讯云环境清理和恢复

### 1. 清理错误部署
- 停止之前错误的容器：`docker compose down`
- 删除不完整的目录：`sudo rm -rf /mnt/datadisk0/volumns/dify`

### 2. 完整恢复
- 解压备份文件到目标位置：`cd /mnt/datadisk0/volumns && tar -xzf /tmp/dify_complete.tar.gz`
- 恢复的目录结构：`/mnt/datadisk0/volumns/dify/dify/`
- 包含所有原始配置文件和数据

### 3. 服务启动
- 进入docker目录：`cd /mnt/datadisk0/volumns/dify/dify/docker`
- 启动所有服务：`docker compose up -d`

## 迁移结果

### 成功启动的容器
- **api**: langgenius/dify-api:1.4.1 (端口5001)
- **web**: langgenius/dify-web:1.4.1 (端口3000)
- **db**: postgres:15-alpine (端口5432)
- **redis**: redis:6-alpine (端口6379)
- **nginx**: nginx:latest (端口280->80, 2443->443)
- **weaviate**: semitechnologies/weaviate:1.19.0
- **worker**: langgenius/dify-api:1.4.1
- **plugin_daemon**: langgenius/dify-plugin-daemon:0.1.1-local (端口5003)
- **sandbox**: langgenius/dify-sandbox:0.2.12
- **ssrf_proxy**: ubuntu/squid:latest (端口3128)

### 服务访问信息
- **HTTP访问**: `http://腾讯云IP:280`
- **HTTPS访问**: `http://腾讯云IP:2443`
- **服务状态**: 正常运行，HTTP 307重定向到/apps

### 解决的关键问题
1. **端口冲突**: 原80端口被占用，映射到280端口
2. **完整性**: 采用完整目录迁移而非单文件迁移
3. **配置保留**: 所有原始配置和数据完整保留

## 下一步计划

### 版本升级准备（1.4.1 -> 1.6.x）
1. 备份当前运行状态
2. 研究1.6.x版本的变更内容
3. 制定升级方案
4. 测试升级过程

### 优化项目
1. 端口规范化（考虑调整为标准80/443端口）
2. SSL证书配置
3. 性能监控配置
4. 备份策略制定

## 经验总结

### 成功因素
1. **完整迁移策略**: 迁移整个目录结构而非单个文件
2. **web传输方式**: 使用nginx传输目录提高传输效率
3. **环境清理**: 彻底清理错误部署避免冲突
4. **原配置保留**: 保持与源服务器完全一致的配置

### 避免的问题
1. 单文件迁移导致的配置缺失
2. 环境变量配置错误
3. 端口冲突导致的启动失败
4. 权限问题导致的文件操作失败

