---
date_created: 2025-07-17 11:42:20
date_modified: 2025-07-17 11:42:20
author: 赵王飞
---




mysql服务部署结果
5.7.44
数据库密码如下
mysql_EbkW86


默认生成的数据目录
/opt/1panel/apps/mysql/mysql/data
/opt/1panel/apps/mysql/mysql/conf/my.cnf
/opt/1panel/apps/mysql/mysql/log

计划使用的数据目录
/mnt/datadisk0/volumns/mysql/prod


先停掉旧的docker,进行数据拷贝
```
sudo cp -r /opt/1panel/apps/mysql/mysql/data /mnt/datadisk0/volumns/mysql/prod

ls -l /mnt/datadisk0/volumns/mysql/prod/    
ls -l /opt/1panel/apps/mysql/mysql/data/
```

将容器的数据目录映射改到datadisk0
```
2025-07-17 11:48:29+08:00 [Note] [Entrypoint]: Entrypoint script for MySQL Server 5.7.44-1.el7 started.
2025-07-17 11:48:30+08:00 [Note] [Entrypoint]: Switching to dedicated user 'mysql'
2025-07-17 11:48:30+08:00 [Note] [Entrypoint]: Entrypoint script for MySQL Server 5.7.44-1.el7 started.
'/var/lib/mysql/mysql.sock' -> '/var/run/mysqld/mysqld.sock'
2025-07-17T03:48:30.605315Z 0 [Note] mysqld (mysqld 5.7.44) starting as process 1 ...
2025-07-17T03:48:30.609198Z 0 [Note] InnoDB: PUNCH HOLE support available
2025-07-17T03:48:30.609219Z 0 [Note] InnoDB: Mutexes and rw_locks use GCC atomic builtins
2025-07-17T03:48:30.609222Z 0 [Note] InnoDB: Uses event mutexes
2025-07-17T03:48:30.609225Z 0 [Note] InnoDB: GCC builtin __atomic_thread_fence() is used for memory barrier
2025-07-17T03:48:30.609227Z 0 [Note] InnoDB: Compressed tables use zlib 1.2.13
2025-07-17T03:48:30.609232Z 0 [Note] InnoDB: Using Linux native AIO
2025-07-17T03:48:30.609512Z 0 [Note] InnoDB: Number of pools: 1
2025-07-17T03:48:30.609640Z 0 [Note] InnoDB: Using CPU crc32 instructions
2025-07-17T03:48:30.611705Z 0 [Note] InnoDB: Initializing buffer pool, total size = 128M, instances = 1, chunk size = 128M
2025-07-17T03:48:30.620260Z 0 [Note] InnoDB: Completed initialization of buffer pool
2025-07-17T03:48:30.622862Z 0 [Note] InnoDB: If the mysqld execution user is authorized, page cleaner thread priority can be changed. See the man page of setpriority().
2025-07-17T03:48:30.634872Z 0 [Note] InnoDB: Highest supported file format is Barracuda.
2025-07-17T03:48:30.645634Z 0 [Note] InnoDB: Creating shared tablespace for temporary tables
2025-07-17T03:48:30.645698Z 0 [Note] InnoDB: Setting file './ibtmp1' size to 12 MB. Physically writing the file full; Please wait ...
2025-07-17T03:48:30.672446Z 0 [Note] InnoDB: File './ibtmp1' size is now 12 MB.
2025-07-17T03:48:30.673105Z 0 [Note] InnoDB: 96 redo rollback segment(s) found. 96 redo rollback segment(s) are active.
2025-07-17T03:48:30.673117Z 0 [Note] InnoDB: 32 non-redo rollback segment(s) are active.
2025-07-17T03:48:30.673608Z 0 [Note] InnoDB: 5.7.44 started; log sequence number 12219447
2025-07-17T03:48:30.673788Z 0 [Note] InnoDB: Loading buffer pool(s) from /var/lib/mysql/ib_buffer_pool
2025-07-17T03:48:30.674174Z 0 [Note] Plugin 'FEDERATED' is disabled.
2025-07-17T03:48:30.676272Z 0 [Note] InnoDB: Buffer pool(s) load completed at 250717 11:48:30
2025-07-17T03:48:30.680166Z 0 [Note] Found ca.pem, server-cert.pem and server-key.pem in data directory. Trying to enable SSL support using them.
2025-07-17T03:48:30.680177Z 0 [Note] Skipping generation of SSL certificates as certificate files are present in data directory.
2025-07-17T03:48:30.680180Z 0 [Warning] A deprecated TLS version TLSv1 is enabled. Please use TLSv1.2 or higher.
2025-07-17T03:48:30.680182Z 0 [Warning] A deprecated TLS version TLSv1.1 is enabled. Please use TLSv1.2 or higher.
2025-07-17T03:48:30.680642Z 0 [Warning] CA certificate ca.pem is self signed.
2025-07-17T03:48:30.680676Z 0 [Note] Skipping generation of RSA key pair as key files are present in data directory.
2025-07-17T03:48:30.680968Z 0 [Note] Server hostname (bind-address): '*'; port: 3306
2025-07-17T03:48:30.681007Z 0 [Note] IPv6 is available.
2025-07-17T03:48:30.681022Z 0 [Note]   - '::' resolves to '::';
2025-07-17T03:48:30.681038Z 0 [Note] Server socket created on IP: '::'.
2025-07-17T03:48:30.683794Z 0 [Warning] Insecure configuration for --pid-file: Location '/var/run/mysqld' in the path is accessible to all OS users. Consider choosing a different directory.
```