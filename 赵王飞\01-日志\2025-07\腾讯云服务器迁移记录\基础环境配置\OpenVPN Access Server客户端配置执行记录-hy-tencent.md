# OpenVPN Access Server客户端配置执行记录 - hy-tencent

## 执行时间
2025-07-17 13:22:00

## 服务器信息
- 公网IP：***************
- 客户端名称：hy-tencent
- 服务器类型：腾讯云
- OpenVPN版本：OpenVPN Access Server (openvpn/openvpn-as:latest)

## 执行步骤记录

### 1. 环境检查
```bash
# 检查当前OpenVPN容器状态
sudo docker ps | grep openvpn
# 结果：发现使用的是OpenVPN Access Server而非社区版
# 容器名：1Panel-openvpn-sNji
# 镜像：openvpn/openvpn-as:latest
# 端口映射：943->943/tcp, 1194->1194/udp, 7443->443/tcp
```

### 2. 检查用户状态
```bash
# 检查用户是否存在
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user hy-tencent UserPropGet
# 结果：用户已存在，但需要设置密码
```

### 3. 配置服务器主机名
```bash
# 设置公网IP为主机名
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --key "host.name" --value "***************" ConfigPut

# 重启服务使配置生效
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli start
```

### 4. 设置用户密码
```bash
# 为用户hy-tencent设置密码
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user hy-tencent --new_pass hy123456 SetLocalPassword
# 结果：密码设置成功
```

### 5. 生成客户端配置
```bash
# 生成客户端配置文件
sudo docker exec 1Panel-openvpn-sNji /usr/local/openvpn_as/scripts/sacli --user hy-tencent GetUserlogin > /tmp/hy-tencent.ovpn
# 结果：成功生成配置文件，包含正确的公网IP地址
```

### 6. 下载配置文件
```bash
# 配置文件已下载到本地：腾讯云服务器配置/hy-tencent.ovpn
```

## 配置文件信息

### 生成的配置文件特点：
- 文件名：hy-tencent.ovpn
- 服务器地址：***************
- 协议：UDP 1194 (主要) + TCP 443 (备用)
- 加密：AES-256-CBC
- 认证方式：用户名密码认证
- 证书有效期：2035-07-15

### 连接信息：
- 用户名：hy-tencent
- 密码：hy123456
- 服务器：***************
- 端口：1194 (UDP) / 443 (TCP)

## 服务状态验证

### 容器运行状态：
```bash
sudo docker ps | grep openvpn
# 结果：容器正常运行，所有端口正确映射
```

### 服务内部状态：
```bash
sudo docker logs 1Panel-openvpn-sNji | tail -5
# 结果：服务正常，客户端配置已生成
```

## 执行结果
✅ 成功生成OpenVPN Access Server客户端配置
✅ 配置文件使用正确的公网IP地址
✅ 用户认证信息设置完成
✅ 配置文件已下载到本地工作区

## 注意事项
1. 使用的是OpenVPN Access Server商业版，不是社区版
2. 需要用户名密码认证，不是证书认证
3. 支持UDP和TCP双协议连接
4. 配置文件包含完整的证书和密钥信息
5. 用户密码为：hy123456（建议后续修改）

## 后续操作建议
1. 将配置文件传输到客户端设备
2. 安装OpenVPN客户端软件
3. 导入配置文件并测试连接
4. 考虑修改用户密码以提高安全性---
date_created: 2025-07-17 13:25:25
date_modified: 2025-07-17 13:25:27
author: 赵王飞
---
