# 汇远融媒项目迁移标准化操作手册

## 1. 迁移概述

### 1.1 迁移架构
```
源环境：K8s集群 + NFS共享存储 + 214数据库
目标环境：Docker容器 + 本地卷挂载 + 腾讯云数据库
传输方式：HTTP直传（通过208服务器main.51zsqc.com域名）
```

### 1.2 核心原则
- **数据安全优先**：完整备份，分步验证
- **服务连续性**：蓝绿部署，最小化停机时间
- **标准化流程**：统一的目录结构和配置模板
- **完整清理**：迁移完成后清理所有临时文件

## 2. 环境信息

### 2.1 服务器信息
| 服务器 | IP地址 | 作用 | 凭证 |
|--------|--------|------|------|
| 208服务器 | ************ | 操作平台/文件中转 | SSH连接 |
| 214服务器 | ************ | 源数据库服务器 | root/TxkjDB2020# |
| 210服务器 | ************ | NFS存储服务器 | SSH连接 |
| 腾讯云服务器 | - | 目标Docker环境 | SSH连接 |

### 2.2 目标环境信息
| 组件 | 容器名 | 凭证 |
|------|--------|------|
| MySQL数据库 | mysql-prod | root/56d9DavJ*zwrwj9rmA |
| 业务数据库用户 | pearproject | kG7#tPq9@zR2$vX1 |
| Docker网络 | 1panel-network | - |

## 3. 迁移前准备

### 3.1 环境检查清单
- [ ] 确认208服务器mysqldump工具可用
- [ ] 确认腾讯云服务器Docker环境正常
- [ ] 确认1panel-network网络存在
- [ ] 确认mysql-prod容器运行正常
- [ ] 确认文件传输域名main.51zsqc.com可访问

### 3.2 项目信息收集
- [ ] 确认项目名称和实例标识
- [ ] 确认相关数据库列表
- [ ] 确认NFS存储路径
- [ ] 确认K8s容器镜像信息
- [ ] 确认端口和域名配置

## 4. 标准化迁移流程

### 4.1 阶段一：数据库迁移

#### 步骤1：导出源数据库
```bash
# 在208服务器执行
mysqldump -h ************ -u root -p'TxkjDB2020#' \
  --single-transaction --routines --triggers [数据库名] > /tmp/[数据库名].sql

# 检查文件大小
ls -lh /tmp/[数据库名].sql
```

#### 步骤2：压缩并提供下载
```bash
# 在208服务器执行
tar -czf /usr/share/nginx/html/transfer/[数据库名].sql.tar.gz /tmp/[数据库名].sql

# 检查压缩文件
ls -lh /usr/share/nginx/html/transfer/[数据库名].sql.tar.gz
```

#### 步骤3：下载到目标服务器
```bash
# 在腾讯云服务器执行
wget -O /tmp/[数据库名].sql.tar.gz https://main.51zsqc.com/transfer/[数据库名].sql.tar.gz

# 解压文件
cd /tmp && tar -xzf [数据库名].sql.tar.gz
mv /tmp/tmp/[数据库名].sql /tmp/[数据库名].sql  # 如果需要
```

#### 步骤4：处理SQL文件并导入
```bash
# 复制到容器
docker cp /tmp/[数据库名].sql mysql-prod:/tmp/

# 创建数据库
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "CREATE DATABASE IF NOT EXISTS [数据库名] DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 处理GTID问题
docker exec mysql-prod bash -c "sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' /tmp/[数据库名].sql > /tmp/[数据库名]_clean.sql"

# 导入数据库
docker exec mysql-prod bash -c "mysql -u root -p'56d9DavJ*zwrwj9rmA' [数据库名] < /tmp/[数据库名]_clean.sql"
```

#### 步骤5：配置权限和验证
```bash
# 配置用户权限
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "GRANT ALL PRIVILEGES ON [数据库名].* TO 'pearproject'@'%'; FLUSH PRIVILEGES;"

# 验证导入成功
docker exec mysql-prod mysql -u root -p'56d9DavJ*zwrwj9rmA' \
  -e "USE [数据库名]; SHOW TABLES;" | wc -l
```

### 4.2 阶段二：应用文件迁移

#### 步骤1：从NFS存储打包应用文件
```bash
# 在210服务器执行（或通过208远程执行）
cd /data/nfs_share/html/HOME_PROD/
tar -czf /tmp/[项目名]_files.tar.gz [项目目录]/

# 传输到208服务器（如果需要）
scp /tmp/[项目名]_files.tar.gz root@************:/usr/share/nginx/html/transfer/
```

#### 步骤2：下载应用文件
```bash
# 在腾讯云服务器执行
wget -O /tmp/[项目名]_files.tar.gz https://main.51zsqc.com/transfer/[项目名]_files.tar.gz

# 解压到目标目录
mkdir -p /mnt/datadisk0/volumns/java-web/[项目名]/
tar -xzf /tmp/[项目名]_files.tar.gz -C /mnt/datadisk0/volumns/java-web/[项目名]/ --strip-components=1
```

### 4.3 阶段三：容器配置和部署

#### 步骤1：创建应用目录结构
```bash
# 在腾讯云服务器执行
mkdir -p /mnt/datadisk0/apps/java-web/[项目名]/{conf,logs,scripts}
mkdir -p /mnt/datadisk0/volumns/java-web/[项目名]/{workdir,ui,logs}
```

#### 步骤2：配置Docker Compose
```yaml
# /mnt/datadisk0/apps/java-web/[项目名]/docker-compose.yml
version: '3.8'

networks:
  1panel-network:
    external: true

services:
  [项目名]-backend:
    image: [镜像ID]
    container_name: [项目名]
    networks:
      - 1panel-network
    volumes:
      - /mnt/datadisk0/volumns/java-web/[项目名]/workdir:/workdir
      - /mnt/datadisk0/volumns/java-web/[项目名]/logs:/home/<USER>/logs
      - /mnt/datadisk0/volumns/java-web/[项目名]/ui:/home/<USER>/projects/ruoyi-ui
    environment:
      - TZ=Asia/Shanghai
      - SPRING_PROFILES_ACTIVE=prod
    restart: unless-stopped
    ports:
      - "[外部端口]:80"
```

#### 步骤3：启动和验证服务
```bash
# 启动容器
cd /mnt/datadisk0/apps/java-web/[项目名]/
docker compose up -d

# 检查容器状态
docker ps | grep [项目名]

# 检查日志
docker logs [项目名] --tail 50

# 测试API接口
curl -I http://localhost:[外部端口]/admin-api/infra/config/list
```

## 5. 清理和验证

### 5.1 临时文件清理
```bash
# 208服务器清理
rm /usr/share/nginx/html/transfer/[数据库名].sql.tar.gz
rm /usr/share/nginx/html/transfer/[项目名]_files.tar.gz
rm /tmp/[数据库名].sql

# 腾讯云服务器清理
rm /tmp/[数据库名].sql.tar.gz /tmp/[数据库名].sql
rm /tmp/[项目名]_files.tar.gz

# 容器内清理
docker exec mysql-prod rm /tmp/[数据库名].sql /tmp/[数据库名]_clean.sql
```

### 5.2 功能验证清单
- [ ] 数据库连接正常
- [ ] 应用启动成功
- [ ] API接口响应正常
- [ ] 前端页面可访问
- [ ] 文件上传下载功能正常
- [ ] 日志输出正常

## 6. 常见问题和解决方案

### 6.1 GTID冲突问题
**现象**：ERROR 1840 (HY000) @@GLOBAL.GTID_PURGED can only be set when @@GLOBAL.GTID_EXECUTED is empty
**解决**：删除SQL文件中的GTID设置行
```bash
sed '/-- GTID state at the end of the backup/,/[0-9]*.*;$/d' source.sql > clean.sql
```

### 6.2 数据库名映射问题
**现象**：应用连接错误的数据库名
**解决**：替换SQL文件中的数据库名引用
```bash
sed 's/old_db_name\./new_db_name\./g' source.sql > corrected.sql
```

### 6.3 RabbitMQ队列依赖问题
**现象**：NOT_FOUND - no queue 'mq_*'
**解决**：手动创建所需队列
```bash
docker exec rabbitmq-prod rabbitmqctl eval 'rabbit_amqqueue:declare({resource, <<"/">>, queue, <<"队列名">>}, true, false, [], none, <<"hyxx">>).'
```

## 7. 迁移记录模板

### 7.1 迁移前记录
- 项目名称：
- 源数据库列表：
- 源文件路径：
- 预计停机时间：
- 负责人：

### 7.2 迁移后记录
- 迁移完成时间：
- 数据库表数量：
- 容器运行状态：
- API测试结果：
- 遗留问题：

这个标准化操作手册基于hyrm-imppc的成功迁移经验总结，可以作为后续汇远融媒项目迁移的标准流程。
