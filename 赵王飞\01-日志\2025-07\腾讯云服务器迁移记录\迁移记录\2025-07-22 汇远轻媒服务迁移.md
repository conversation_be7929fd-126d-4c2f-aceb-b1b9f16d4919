# 背景
接下来需要迁移的一个业务是在在旧的服务器运行如下, 这个容器的镜像没有提交到docker仓库, 而且容器内部可能有一些调整没有提交成为镜像，所以需要先提交一个v2版本的镜像,然后将 v1和 v2都 push 到docker仓库.
505df9a8ce27        zhao0829wang/hyqm-django:3.12.7-v1               "supervisord"            3 weeks ago         Up 2 weeks                      0.0.0.0:9005->8000/tcp                                                                                        hyqm-django

# 旧服务器操作命令

由于服务器与 Docker Hub 网络连接不稳定，导致 `docker push` 失败。现采用 `docker save`/`load` 的离线方式进行迁移。

```bash
# --- 步骤一：将正在运行的容器提交为新镜像 v2 ---
# 这会把容器当前的文件系统状态保存下来，包括任何可能的手动修改。
docker commit e4931005cae2 zhao0829wang/hyqm-django:3.12.7-v2

# --- 步骤二：将 v1 和 v2 两个镜像分别打包成 .tar 文件 ---
docker save -o hyqm-django-v1.tar zhao0829wang/hyqm-django:3.12.7-v1
docker save -o hyqm-django-v2.tar zhao0829wang/hyqm-django:3.12.7-v2
```

完成上述操作后，请将生成的 `hyqm-django-v1.tar` 和 `hyqm-django-v2.tar` 文件传输到新服务器。

# 新服务器操作命令

```bash
# --- 步骤一：从 .tar 文件加载镜像 ---
docker load -i hyqm-django-v1.tar
docker load -i hyqm-django-v2.tar

# --- 步骤二：验证镜像是否加载成功 ---
docker images | grep hyqm-django
```


# 代码部署
首先需要克隆代码
git长久保存用户名密码

```bash
git config --global credential.helper store
git clone https://gitee.com/imut-iot/crawl.all.git hyqm2-prod
cp -r hyqm2-prod hyqm2-test
cd /mnt/datadisk0/volumns/hyqm2
ls
# 两个环境都已存在
hyqm2-prod  hyqm2-test
```


```bash
# 先启动一个测试django
sudo docker rm -f hyqm2-test

sudo docker run -d \
--restart=always \
--name hyqm2-test \
--network 1panel-network \
-p 9005:8000 \
-e TZ=Asia/Shanghai \
-e DJANGO_ENV=tencent_test \
-v /mnt/datadisk0/volumns/hyqm2/hyqm2-test/django-vue3-admin/backend:/backend \
-w /backend \
-v /mnt/datadisk0/volumns/hyqm2/hyqm2-test/django-vue3-admin/backend/supervisord-dev.conf:/etc/supervisor/supervisord.conf \
zhao0829wang/hyqm-django:3.12.7-v1 supervisord
```

```sql



#### 日志目录不存在导致启动test容器出错 
```bash
# 使用 -p 参数确保即使父目录不存在也能被创建
mkdir -p /mnt/datadisk0/volumns/hyqm2/hyqm2-test/django-vue3-admin/backend/logs
```

最核心原因
容器无法启动的最核心原因是：Supervisord 启动时，其配置文件 supervisord.conf 中指定的日志目录 /backend/logs/ 在容器内不存在。

原因分析
错误信息解读： 您提供的日志中反复出现以下关键错误：
```
Error: The directory named as part of the path /backend/logs/django.out.log does not exist in section 'program:django'
```

管理员
hyxx
管理员密码
hyxx-test123
Dashboard 端口
15673
服务端口
5673

## 环境文件调整
新增
env_tencent_test.py
```python
DATABASE_HOST = 'mysql-test'
# # 数据库端口
DATABASE_PORT = 3306
# # 数据库用户名
DATABASE_USER = "root"
# # 数据库密码
DATABASE_PASSWORD = 'FKPMuj2kQ7'
# # 数据库名称
DATABASE_NAME = "hyqm2_test"
REDIS_HOST = "redis-test"
REDIS_PORT = 6379
RABBITMQ_HOST='rabbitmq-test'
RABBITMQ_URL= 'amqp://hyxx:hyxx-test123@rabbitmq-test:5673'
```

# 生产环境部署方案

## 1. 准备生产环境配置文件

在新服务器的 `/mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend/` 目录下，创建 `env_tencent_prod.py` 文件，内容如下：

```python
# 生产环境 (env_tencent_prod.py)

# 数据库配置
DATABASE_HOST = 'mysql-prod'
DATABASE_PORT = 3306
DATABASE_USER = "hyqm_prod"
DATABASE_PASSWORD = 'zbQBVTwNUT#j@T*n'
DATABASE_NAME = "hyqm2"

# Redis 配置
REDIS_HOST = "redis-prod"
REDIS_PORT = 6379

# RabbitMQ 配置 (请确认生产环境的用户和密码)
RABBITMQ_HOST = 'rabbitmq-prod'
RABBITMQ_URL = 'amqp://hyxx:hyxx123@rabbitmq-prod:5673'
```

同时，请确保生产环境的 Supervisord 配置文件 `supervisord-prod.conf` 也已准备就绪。

## 2. 初始化生产数据库

```bash
# --- 步骤一：获取 mysql-prod 容器的 root 密码 ---
docker inspect mysql-prod | grep MYSQL_ROOT_PASSWORD

# --- 步骤二：创建数据库和用户 (请将 YOUR_ROOT_PASSWORD 替换为上一步获取到的密码) ---
docker exec mysql-prod mysql -uroot -p'YOUR_ROOT_PASSWORD' -e " \
CREATE DATABASE IF NOT EXISTS hyqm2 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci; \
CREATE USER 'hyqm_prod'@'%' IDENTIFIED BY 'zbQBVTwNUT#j@T*n'; \
GRANT ALL PRIVILEGES ON hyqm2.* TO 'hyqm_prod'@'%'; \
FLUSH PRIVILEGES;"
```

### 数据库创建执行记录

- **任务**: 创建测试数据库 `hyqm2_test`。
- **选择原因**: 根据任务要求，需要为测试环境准备数据库。
- **实施内容**:
    1.  **初次尝试失败**: 使用文档中 `env_tencent_test.py` 记录的密码 (`FKPMuj2kQ7`) 尝试连接，但访问被拒绝。
        - **原始错误信息**:
          ```
          ERROR 1045 (28000): Access denied for user 'root'@'localhost' (using password: YES)
          ```
    2.  **排查与解决**: 通过 `docker inspect` 命令查找容器的环境变量，获取了正确的 root 密码。
        - **执行的诊断命令**:
          ```bash
          docker inspect mysql-test | grep MYSQL_ROOT_PASSWORD
          ```
        - **获取的密码**:
          ```
          "MYSQL_ROOT_PASSWORD=mysql_faQhf7"
          ```
    3.  **名称修正**: 根据用户反馈，数据库名称应使用下划线 (`hyqm2_test`) 而非中划线。已执行更正操作。
        - **修正命令**:
          ```bash
          docker exec mysql-test mysql -uroot -p'mysql_faQhf7' -e "DROP DATABASE IF EXISTS \`hyqm2-test\`; CREATE DATABASE IF NOT EXISTS \`hyqm2_test\` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
          ```

### 生产数据迁移至测试库执行记录

- **任务**: 将生产数据库 `hyqm2` 的数据完整复制到测试数据库 `hyqm2_test`。
- **选择原因**: 用户要求同步生产数据到测试环境，以便进行更真实的测试。
- **实施内容**:
    1.  **初始方案 (管道传输)**: 尝试使用 `mysqldump` 管道直接传输数据，但因数据量较大，命令执行超时。
        - **执行的命令 (失败)**:
          ```bash
          docker exec mysql-prod mysqldump -uhyqm_prod -p'zbQBVTwNUT#j@T*n' hyqm2 | docker exec -i mysql-test mysql -uroot -p'mysql_faQhf7' hyqm2_test
          ```
    2.  **修订方案 (文件导入)**: 为避免超时，采用更稳健的三步法：先导出到文件，再导入，最后清理。
        - **步骤一：导出数据到文件**:
          ```bash
          docker exec mysql-prod mysqldump -uhyqm_prod -p'zbQBVTwNUT#j@T*n' hyqm2 > /mnt/datadisk0/hyqm2_dump.sql
          ```
        - **步骤二：从文件导入数据**:
          ```bash
          docker exec -i mysql-test mysql -uroot -p'mysql_faQhf7' hyqm2_test < /mnt/datadisk0/hyqm2_dump.sql
          ```
        - **步骤三：清理临时文件**:
          ```bash
          rm /mnt/datadisk0/hyqm2_dump.sql
          ```
- **结果**: 数据成功迁移。

## 3. 启动生产环境容器

```bash
# --- 步骤一：创建日志目录 ---
mkdir -p /mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend/logs

# --- 步骤二：启动生产容器 (使用 v2 镜像) ---
sudo docker rm -f hyqm2-prod
sudo docker run -d \
--restart=always \
--name hyqm2-prod \
--network 1panel-network \
-p 9006:8000 \
-e TZ=Asia/Shanghai \
-e DJANGO_ENV=tencent_prod \
-v /mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend:/backend \
-w /backend \
-v /mnt/datadisk0/volumns/hyqm2/hyqm2-prod/django-vue3-admin/backend/supervisord-prod.conf:/etc/supervisor/supervisord.conf \
zhao0829wang/hyqm-django:3.12.7-v2 supervisord
```
