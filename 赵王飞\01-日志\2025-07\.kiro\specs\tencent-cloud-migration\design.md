# Design Document

## Overview

本设计文档详细描述了腾讯云业务迁移项目的技术架构和实施方案。项目基于Ubuntu Server 24.04 LTS系统，采用Docker容器化技术和1Panel管理平台，实现从双创云到腾讯云的平稳迁移。

## 系统环境信息

### 服务器配置
- **操作系统**: Ubuntu Server 24.04 LTS 64位
- **CPU**: 8核
- **内存**: 32GB (30Gi可用)
- **系统盘**: 50GB
- **数据盘**: 500GB (挂载到 /mnt/datadisk0)
- **网络**: 5Mbps公网带宽

### 系统资源状态（重装后）
```
               total        used        free      shared  buff/cache   available
Mem:            30Gi       688Mi        29Gi       2.5Mi       707Mi        30Gi
Swap:          1.9Gi          0B       1.9Gi
```
**注意**: Ubuntu 24.04 LTS默认配置了1.9GB Swap空间，相比TencentOS Server 4的0B Swap有所增加。

## Architecture

### 整体架构图

```
                              外部访问
                                 │
                    ┌────────────▼────────────┐
                    │      雷池WAF防火墙       │
                    │    (可选入口网关)        │
                    └────────────┬────────────┘
                                 │
┌─────────────────────────────────▼───────────────────────────────┐
│                        腾讯云服务器                              │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                  Ubuntu 24.04 LTS                          │ │
│  │  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐        │ │
│  │  │   1Panel    │  │   Docker    │  │  监控工具    │        │ │
│  │  │  管理面板    │  │   Engine    │  │             │        │ │
│  │  └─────────────┘  └─────────────┘  └─────────────┘        │ │
│  │                                                            │ │
│  │  ┌─────────────────────────────────────────────────────┐  │ │
│  │  │                Docker 容器集群                      │  │ │
│  │  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────┐ │  │ │
│  │  │ │PHP网站  │ │Web应用  │ │  Dify   │ │   雷池WAF   │ │  │ │
│  │  │ └─────────┘ └─────────┘ └─────────┘ └─────────────┘ │  │ │
│  │  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────────┐ │  │ │
│  │  │ │ MySQL   │ │ Redis   │ │Memcache │ │  RabbitMQ   │ │  │ │
│  │  │ └─────────┘ └─────────┘ └─────────┘ └─────────────┘ │  │ │
│  │  └─────────────────────────────────────────────────────┘  │ │
│  └─────────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────────┘
```

### 存储架构

```
系统盘 (50GB)                    数据盘 (500GB)
├── /                           ├── /mnt/datadisk0/
├── /boot                       ├── /mnt/datadisk0/docker/
├── /var (系统日志)              │   ├── containers/
├── /usr                        │   ├── images/
└── /home                       │   └── volumes/
                                ├── /mnt/datadisk0/1panel/
                                ├── /mnt/datadisk0/backups/
                                └── /mnt/datadisk0/data/
```

## Components and Interfaces

### 1. 基础系统组件

#### 1.1 Ubuntu 24.04 LTS 基础配置
- **包管理器**: APT
- **防火墙**: UFW (Uncomplicated Firewall)
- **SSH服务**: OpenSSH Server
- **时间同步**: systemd-timesyncd
- **日志管理**: systemd-journald

#### 1.2 数据盘挂载配置
```bash
# 数据盘挂载点
/dev/vdb -> /mnt/datadisk0 (ext4文件系统)

# 自动挂载配置
/etc/fstab: /dev/vdb /mnt/datadisk0 ext4 defaults 0 2
```

### 2. Docker容器平台

#### 2.1 Docker Engine配置
```json
{
  "data-root": "/mnt/datadisk0/docker",
  "storage-driver": "overlay2",
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  },
  "registry-mirrors": [
    "https://mirror.ccs.tencentyun.com"
  ]
}
```

#### 2.2 Docker镜像管理策略
- **官方镜像**: 从Docker Hub拉取MySQL、Redis、Memcache、RabbitMQ等官方镜像
- **自定义镜像迁移**: 
  - 从双创云服务器导出自编译镜像：`docker save -o image.tar custom-image:tag`
  - 推送到Docker Hub私有仓库：`docker push username/custom-image:tag`
  - 在新服务器拉取：`docker pull username/custom-image:tag`
- **镜像版本管理**: 使用语义化版本标签管理镜像版本
- **镜像清理**: 定期清理未使用的镜像以节省存储空间
- **镜像加速**: 配置腾讯云镜像加速器提高拉取速度

#### 2.3 Docker网络配置
- **默认网络**: bridge
- **自定义网络**: app-network (用于应用间通信)
- **端口映射**: 通过1Panel管理界面配置
- **网络隔离**: 不同业务使用独立的Docker网络

### 3. 1Panel管理平台

#### 3.1 1Panel核心功能
- **容器管理**: Docker容器的创建、启动、停止、删除
- **镜像管理**: Docker镜像的拉取、构建、删除
- **文件管理**: 服务器文件的上传、下载、编辑
- **系统监控**: CPU、内存、磁盘、网络使用情况
- **备份管理**: 数据备份和恢复功能

#### 3.2 1Panel安装配置
```bash
# 安装目录: /opt/1panel
# 数据目录: /mnt/datadisk0/1panel
# Web端口: 10086 (HTTPS)
# 面板入口: https://服务器IP:10086/安全入口
```

### 4. 业务应用容器

#### 4.1 容器分类
- **Web应用容器**: 前端应用、API服务、PHP网站
- **数据库容器**: MySQL、Redis、Memcache等
- **中间件容器**: RabbitMQ、消息队列等
- **AI服务容器**: Dify服务
- **安全防护容器**: 雷池WAF防火墙
- **工具容器**: 定时任务、数据处理等

#### 4.2 关键服务详细配置

##### 4.2.1 数据库服务
```yaml
# MySQL配置
mysql:
  image: mysql:8.0
  environment:
    MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
  volumes:
    - /mnt/datadisk0/data/mysql:/var/lib/mysql
  resources:
    memory: "2Gi"
    cpu: "1.0"

# Redis配置
redis:
  image: redis:7-alpine
  volumes:
    - /mnt/datadisk0/data/redis:/data
  resources:
    memory: "512Mi"
    cpu: "0.5"

# Memcache配置
memcache:
  image: memcached:1.6-alpine
  resources:
    memory: "256Mi"
    cpu: "0.25"
```

##### 4.2.2 中间件服务
```yaml
# RabbitMQ配置
rabbitmq:
  image: rabbitmq:3-management
  environment:
    RABBITMQ_DEFAULT_USER: ${RABBITMQ_USER}
    RABBITMQ_DEFAULT_PASS: ${RABBITMQ_PASS}
  volumes:
    - /mnt/datadisk0/data/rabbitmq:/var/lib/rabbitmq
  resources:
    memory: "1Gi"
    cpu: "0.5"
```

##### 4.2.3 AI服务
```yaml
# Dify服务配置
dify:
  image: langgenius/dify-web:latest
  environment:
    - API_URL=${DIFY_API_URL}
    - APP_URL=${DIFY_APP_URL}
  volumes:
    - /mnt/datadisk0/data/dify:/app/data
  resources:
    memory: "2Gi"
    cpu: "1.0"
```

##### 4.2.4 安全防护与网关选择

**方案A: 雷池WAF作为入口网关（推荐）**
```yaml
# 雷池WAF配置
safeline:
  image: chaitin/safeline-tengine:latest
  ports:
    - "80:80"
    - "443:443"
  volumes:
    - /mnt/datadisk0/data/safeline:/etc/nginx
    - /mnt/datadisk0/data/safeline/ssl:/etc/ssl
  environment:
    - SAFELINE_PG_PASSWORD=${SAFELINE_DB_PASSWORD}
  resources:
    memory: "1Gi"
    cpu: "0.5"
```

**方案B: 传统Nginx网关**
```yaml
# Nginx网关配置
nginx-gateway:
  image: nginx:alpine
  ports:
    - "80:80"
    - "443:443"
  volumes:
    - /mnt/datadisk0/data/nginx/conf.d:/etc/nginx/conf.d
    - /mnt/datadisk0/data/nginx/ssl:/etc/ssl
  resources:
    memory: "256Mi"
    cpu: "0.25"
```

**网关选择对比分析:**
- **雷池WAF优势**: 
  - 集成WAF防护功能，自动防御常见Web攻击
  - 可视化安全管理界面
  - 支持IP黑白名单、CC防护等
  - 适合安全要求较高的场景
- **传统Nginx优势**:
  - 资源占用更少
  - 配置更灵活
  - 社区支持更广泛
  - 适合资源受限或简单场景

**推荐方案**: 使用雷池WAF作为入口网关，原因：
1. 提供额外的安全防护层
2. 减少安全配置复杂度
3. 适合非专职运维团队
4. 成本增加有限但安全收益显著

#### 4.3 PHP网站容器配置
```yaml
# PHP-FPM + Nginx配置
php-websites:
  php-fpm:
    image: php:8.2-fpm
    volumes:
      - /mnt/datadisk0/data/websites:/var/www/html
    resources:
      memory: "1Gi"
      cpu: "0.5"
  
  nginx:
    image: nginx:alpine
    volumes:
      - /mnt/datadisk0/data/websites:/var/www/html
      - /mnt/datadisk0/data/nginx/conf.d:/etc/nginx/conf.d
    resources:
      memory: "256Mi"
      cpu: "0.25"
```

#### 4.4 容器资源限制策略
```yaml
# 资源分配策略
resource_allocation:
  high_priority:  # 数据库、AI服务
    memory: "2Gi"
    cpu: "1.0"
  
  medium_priority:  # Web应用、中间件
    memory: "1Gi"
    cpu: "0.5"
  
  low_priority:  # 工具、缓存
    memory: "512Mi"
    cpu: "0.25"
```

## Data Models

### 1. 容器配置模型
```yaml
Container:
  name: string
  image: string
  ports: 
    - containerPort: number
      hostPort: number
  volumes:
    - hostPath: string
      containerPath: string
  environment:
    - name: string
      value: string
  resources:
    limits:
      memory: string
      cpu: string
```

### 2. 备份配置模型
```yaml
Backup:
  name: string
  source: string
  destination: string
  schedule: string (cron格式)
  retention: number (保留天数)
  compression: boolean
```

## Error Handling

### 1. 系统级错误处理
- **磁盘空间不足**: 监控磁盘使用率，超过80%时告警
- **内存不足**: 配置Swap空间，监控内存使用情况
- **网络连接问题**: 配置网络重试机制和超时设置
- **服务异常**: 配置systemd服务自动重启

### 2. Docker相关错误处理
- **容器启动失败**: 检查镜像完整性和配置正确性
- **镜像拉取失败**: 配置镜像加速器和重试机制
- **存储空间不足**: 定期清理无用镜像和容器
- **网络连接问题**: 检查Docker网络配置

### 3. 1Panel相关错误处理
- **面板无法访问**: 检查防火墙和端口配置
- **操作权限问题**: 验证用户权限和文件所有权
- **数据库连接失败**: 检查数据库服务状态和连接配置

## Testing Strategy

### 1. 系统测试
- **基础功能测试**: 验证系统基本功能正常
- **性能测试**: 测试系统在负载下的表现
- **稳定性测试**: 长时间运行测试
- **安全测试**: 验证安全配置和访问控制

### 2. Docker测试
- **容器生命周期测试**: 创建、启动、停止、删除容器
- **镜像管理测试**: 拉取、构建、推送镜像
- **网络连通性测试**: 容器间和外部网络通信
- **数据持久化测试**: 数据卷挂载和数据保持

### 3. 1Panel测试
- **Web界面测试**: 所有管理功能正常工作
- **权限控制测试**: 用户权限和访问控制
- **备份恢复测试**: 数据备份和恢复功能
- **监控告警测试**: 系统监控和告警机制

### 4. 业务迁移测试
- **数据迁移测试**: 验证数据完整性和一致性
- **应用功能测试**: 确保所有业务功能正常
- **性能对比测试**: 对比迁移前后的性能表现
- **回滚测试**: 验证紧急回滚方案的可行性

## Security Considerations

### 1. 系统安全
- **SSH密钥认证**: 禁用密码登录，使用密钥认证
- **防火墙配置**: 只开放必要端口
- **系统更新**: 定期更新系统补丁
- **用户权限**: 最小权限原则

### 2. Docker安全
- **镜像安全**: 使用官方镜像或可信镜像源
- **容器隔离**: 合理配置容器权限和资源限制
- **网络安全**: 使用自定义网络隔离容器
- **数据加密**: 敏感数据加密存储

### 3. 1Panel安全
- **访问控制**: 强密码和IP白名单
- **HTTPS加密**: 启用SSL证书
- **会话管理**: 合理的会话超时设置
- **操作审计**: 记录所有管理操作

## Performance Optimization

### 1. 系统优化
- **内核参数调优**: 优化网络和文件系统参数
- **磁盘I/O优化**: 使用SSD存储和合理的文件系统
- **内存管理**: 合理配置Swap和内存缓存
- **CPU调度**: 优化进程调度策略

### 2. Docker优化
- **镜像优化**: 使用多阶段构建减小镜像大小
- **存储优化**: 使用overlay2存储驱动
- **网络优化**: 合理配置网络模式和参数
- **资源限制**: 为容器设置合理的资源限制

### 3. 应用优化
- **负载均衡**: 使用Nginx进行负载均衡
- **缓存策略**: 合理使用Redis等缓存
- **数据库优化**: 优化数据库配置和查询
- **静态资源**: 使用CDN加速静态资源

## Monitoring and Alerting

### 1. 系统监控
- **资源监控**: CPU、内存、磁盘、网络使用率
- **服务监控**: 关键服务的运行状态
- **日志监控**: 系统和应用日志分析
- **性能监控**: 响应时间和吞吐量

### 2. 告警机制
- **阈值告警**: 资源使用超过阈值时告警
- **服务异常告警**: 服务停止或异常时告警
- **磁盘空间告警**: 磁盘使用率过高时告警
- **网络异常告警**: 网络连接问题时告警

### 3. 告警通知
- **邮件通知**: 发送告警邮件
- **短信通知**: 紧急情况短信通知
- **企业微信**: 集成企业微信通知
- **日志记录**: 所有告警记录到日志文件