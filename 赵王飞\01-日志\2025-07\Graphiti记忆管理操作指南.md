# Graphiti记忆管理操作指南

## 概述

Graphiti MCP服务器提供了一套完整的记忆管理工具，允许AI助手通过MCP协议与Graphiti的知识图谱进行交互。本指南详细说明如何通过客户端查看、增删改查记忆数据。

## 可用的MCP工具

### 1. 记忆存储工具

#### add_episode (add_memory)
- **功能**: 向知识图谱添加记忆片段
- **支持格式**: 文本、JSON、消息格式
- **用途**: 存储用户交互、偏好设置、程序流程等信息
- **最佳实践**: 
  - 立即捕获用户需求和偏好
  - 将长需求拆分为逻辑块
  - 明确标注更新的信息
  - 使用清晰的类别标签

**JSON数据处理示例**:
```json
{
  "name": "Customer Profile",
  "episode_body": "{\"company\": {\"name\": \"Acme Technologies\"}, \"products\": [{\"id\": \"P001\", \"name\": \"CloudSync\"}, {\"id\": \"P002\", \"name\": \"DataMiner\"}]}",
  "source": "json",
  "source_description": "CRM data"
}
```

### 2. 记忆检索工具

#### search_nodes
- **功能**: 搜索知识图谱中的相关节点摘要
- **用途**: 查找实体信息、偏好设置、程序流程
- **搜索策略**: 
  - 按实体类型过滤（Preference、Procedure、Requirement）
  - 使用center_node_uuid进行关联搜索
  - 优先考虑具体匹配

#### search_facts
- **功能**: 搜索知识图谱中的相关事实（实体间的边）
- **用途**: 发现关系和事实信息
- **应用场景**: 查找实体间的连接和依赖关系

#### get_episodes
- **功能**: 检索最近的记忆片段
- **用途**: 获取上下文信息，了解最近的交互历史
- **应用**: 为当前任务提供历史背景

### 3. 记忆删除工具

#### delete_episode
- **功能**: 从知识图谱中删除指定的记忆片段
- **用途**: 移除过时或错误的信息
- **注意事项**: 删除操作不可逆，需谨慎使用

#### delete_entity_edge
- **功能**: 删除实体间的边（关系）
- **用途**: 移除特定的关系连接
- **应用场景**: 更正错误的实体关系

### 4. 图谱维护工具

#### clear_graph
- **功能**: 清空整个知识图谱并重建索引
- **用途**: 完全重置记忆系统
- **警告**: 这将删除所有数据，仅在必要时使用

#### get_status
- **功能**: 获取服务器和Neo4j连接状态
- **用途**: 检查系统健康状况
- **应用**: 故障排除和系统监控

#### get_entity_edge
- **功能**: 通过UUID获取特定的实体边
- **用途**: 查看特定关系的详细信息
- **应用**: 精确查询和验证

## 操作最佳实践

### 开始任务前
1. **总是先搜索**: 使用search_nodes查找相关偏好和程序
2. **搜索事实**: 使用search_facts发现相关关系信息
3. **按类型过滤**: 指定Preference、Procedure或Requirement进行目标搜索
4. **审查所有匹配**: 仔细检查与当前任务匹配的偏好、程序或事实

### 信息存储策略
1. **立即捕获**: 用户表达需求或偏好时立即使用add_memory存储
2. **明确文档程序**: 发现用户工作方式时记录为程序
3. **记录事实关系**: 学习实体间连接时存储为事实
4. **使用明确类别**: 为偏好和程序使用清晰的类别标签

### 工作期间
1. **尊重发现的偏好**: 根据找到的偏好调整工作
2. **严格遵循程序**: 如果找到当前任务的程序，严格按步骤执行
3. **应用相关事实**: 使用事实信息指导决策和建议
4. **保持一致性**: 与之前识别的偏好、程序和事实保持一致

### 搜索策略
1. **建议前先搜索**: 提出建议前总是检查已建立的知识
2. **结合节点和事实搜索**: 对于复杂任务，同时搜索节点和事实
3. **使用center_node_uuid**: 探索相关信息时围绕特定节点搜索
4. **优先具体匹配**: 具体信息优先于一般信息
5. **主动识别模式**: 注意用户行为模式，考虑存储为偏好或程序

## 组管理和命名空间

### group_id的使用
- **功能**: 为图谱数据设置命名空间
- **用途**: 多用户场景下的数据组织
- **配置**: 通过--group-id参数或环境变量设置
- **默认值**: "default"

### 多租户支持
- 使用group_id过滤相关数据组
- 为不同上下文组织数据
- 支持企业级多用户部署

## 环境配置

### 并发控制
- **SEMAPHORE_LIMIT**: 控制并发操作数量（默认：10）
- **用途**: 防止LLM提供商的429速率限制错误
- **调整策略**: 
  - 遇到速率限制时降低值
  - LLM提供商支持更高吞吐量时增加值

### 自定义实体类型
- **--use-custom-entities**: 启用预定义实体类型的实体提取
- **用途**: 领域特定的实体识别
- **配置**: 通过Pydantic模型定义自定义实体

## 故障排除

### 常见问题
1. **连接问题**: 使用get_status检查服务器和数据库状态
2. **搜索无结果**: 检查group_id设置和数据存在性
3. **性能问题**: 调整SEMAPHORE_LIMIT值
4. **数据不一致**: 考虑使用clear_graph重置（谨慎使用）

### 监控和维护
- 定期检查get_status确保系统健康
- 监控Neo4j数据库性能
- 根据使用模式调整并发设置
- 定期备份重要的知识图谱数据

## 记住

**知识图谱就是你的记忆**。持续使用它来提供个性化的协助，尊重用户建立的偏好、程序和事实上下文。通过系统性地搜索、存储和应用知识图谱中的信息，可以为用户提供更加智能和个性化的服务体验。