# 背景
掌上青城目前还在旧服务器上，获取数据连接的是*************:8686，经过和数据供应商沟通，将 IP 白名单换成了新的腾讯云服务器，IP 为 ***************。但是现在，旧的服务器已经无法访问数据源，新的服务器可以，我期望做一个端口转发，在腾讯云服务器连接到目标端口，取得数据后，直接转发到旧服务器。

## 实施方案

根据需求，我们选择使用 `socat` 工具来实现端口转发。此方案具备部署简单、命令直观、易于调试的优点，非常适合当前的场景。

### 1. 安装 socat

通过 SSH 连接到新的腾讯云服务器 (`***************`)，执行以下命令安装 `socat`。

**检查操作系统版本**:
```bash
cat /etc/os-release
```
**输出**:
```text
PRETTY_NAME="Ubuntu 24.04.2 LTS"
NAME="Ubuntu"
VERSION_ID="24.04"
VERSION="24.04.2 LTS (Noble Numbat)"
VERSION_CODENAME=noble
ID=ubuntu
ID_LIKE=debian
HOME_URL="https://www.ubuntu.com/"
SUPPORT_URL="https://help.ubuntu.com/"
BUG_REPORT_URL="https://bugs.launchpad.net/ubuntu/"
PRIVACY_POLICY_URL="https://www.ubuntu.com/legal/terms-and-policies/privacy-policy"
UBUNTU_CODENAME=noble
LOGO=ubuntu-logo
```

**安装命令**:
```bash
sudo apt-get update && sudo apt-get install -y socat
```

### 2. 启动端口转发

执行以下命令，启动端口转发服务。该命令会监听本地 `8686` 端口，并将所有流量转发至 `*************:8686`。使用 `nohup` 和 `&` 确保其在后台持续运行。

**启动命令**:
```bash
nohup socat TCP-LISTEN:8686,fork TCP:*************:8686 >/dev/null 2>&1 &
```

### 3. 验证服务状态

执行以下命令，确认 `socat` 进程已成功在后台运行。

**验证命令**:
```bash
ps aux | grep '[s]ocat TCP-LISTEN:8686'
```

**输出**:
```
ubuntu     58925  0.0  0.0   9288  3712 ?        S    13:11   0:00 socat TCP-LISTEN:8686,fork TCP:*************:8686
```

至此，端口转发已配置完成。旧服务器现在可以将其数据请求指向 `***************:8686` 来获取数据。

## 问题排查：数据传输错误

在初次配置后，发现旧服务器的应用日志中出现大量数据解析错误，如 `Error: 错误的|数量:`。

- **原因分析**: `socat` 作为中间层，改变了TCP数据包在网络中传输的分块方式和时序。虽然数据内容本身没有被修改，但数据到达的“节奏”发生了变化。接收端的应用程序可能依赖于特定的数据包到达模式来解析消息，当数据包被分割或合并时，原有的解析逻辑就会失败。

- **解决方案**: 优化 `socat` 参数，通过添加 `nodelay` 选项来禁用Nagle算法。这可以减少网络缓冲，使数据包更及时地被发送，从而更接近直接连接时的网络行为。

### 4. 优化并重启服务

首先，强制停止旧的 `socat` 进程。

**停止命令**:
```bash
# kill 74479 # 进程ID可能变化
kill -9 74479
```

然后，使用带有 `nodelay` 选项的新命令重启服务。

**优化后的启动命令**:
```bash
nohup socat TCP-LISTEN:8686,fork,nodelay TCP:*************:8686,nodelay >/dev/null 2>&1 &
```

**验证服务状态**:
```bash
ps aux | grep '[s]ocat TCP-LISTEN:8686'
```
**输出**:
```
ubuntu     76316  0.0  0.0   9288  3840 ?        S    13:39   0:00 socat TCP-LISTEN:8686,fork,nodelay TCP:*************:8686,nodelay
ubuntu     76327  0.7  0.0   9288  2168 ?        S    13:39   0:00 socat TCP-LISTEN:8686,fork,nodelay TCP:*************:8686,nodelay
```

看到两个进程是正常现象，一个为监听父进程，一个为处理连接的子进程。这表明优化后的服务已成功启动并正在处理连接。


```
telnet ************* 8686
telnet 111.229.************
```